<template>
  <div class="records-management-container layout-padding">
    <div class="records-management-padding layout-padding-auto layout-padding-view">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2 class="page-title">
          <el-icon><Document /></el-icon>
          检测记录管理
        </h2>
        <p class="page-description">查看和管理所有类型的检测记录</p>
      </div>

      <!-- 记录类型切换 -->
      <div class="record-type-tabs">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="图像检测记录" name="image">
            <div class="tab-content">
              <ImageRecords ref="imageRecordsRef" :user-filter="userFilter" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="视频检测记录" name="video">
            <div class="tab-content">
              <VideoRecords ref="videoRecordsRef" :user-filter="userFilter" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="实时检测记录" name="camera">
            <div class="tab-content">
              <CameraRecords ref="cameraRecordsRef" :user-filter="userFilter" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 统计信息 -->
      <div class="statistics-panel" v-if="statistics">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><Picture /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.imageCount || 0 }}</div>
            <div class="stat-label">图像检测</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><VideoPlay /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.videoCount || 0 }}</div>
            <div class="stat-label">视频检测</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><Camera /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.cameraCount || 0 }}</div>
            <div class="stat-label">实时检测</div>
          </div>
        </div>
        <div class="stat-card total">
          <div class="stat-icon">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
            <div class="stat-label">总计</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="recordsManagement">
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
import { 
  Document, 
  Picture, 
  VideoPlay, 
  Camera, 
  DataAnalysis 
} from '@element-plus/icons-vue';
import ImageRecords from './components/ImageRecords.vue';
import VideoRecords from './components/VideoRecords.vue';
import CameraRecords from './components/CameraRecords.vue';
import request from '/@/utils/request';

const route = useRoute();
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

// 当前激活的标签页
const activeTab = ref('image');

// 组件引用
const imageRecordsRef = ref();
const videoRecordsRef = ref();
const cameraRecordsRef = ref();

// 统计信息
const statistics = ref<any>(null);

// 用户过滤条件
const userFilter = computed(() => {
  // 从URL参数获取用户名，如果没有则使用当前登录用户
  const urlUser = route.query.user as string;
  if (urlUser) {
    console.log('从URL获取用户过滤:', urlUser);
    return urlUser;
  }

  // 如果是管理员，不过滤用户；如果是普通用户，只显示自己的记录
  const currentUser = userInfos.value.userName !== 'admin' ? userInfos.value.userName : '';
  console.log('当前用户过滤:', currentUser);
  return currentUser;
});

// 处理标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
  loadStatistics();
};

// 加载统计信息
const loadStatistics = async () => {
  try {
    const params: any = {};
    if (userFilter.value) {
      params.username = userFilter.value;
    }

    console.log('加载统计信息，用户过滤:', userFilter.value);

    // 使用新的统计API
    const response = await request.get('/api/records/statistics', { params });

    if (response.code === "0" || response.code === 0) {
      statistics.value = response.data;
      console.log('统计信息加载完成:', statistics.value);
    } else {
      console.error('统计信息加载失败:', response.msg);
      statistics.value = {
        imageCount: 0,
        videoCount: 0,
        cameraCount: 0,
        totalCount: 0
      };
    }
  } catch (error) {
    console.error('加载统计信息失败:', error);
    statistics.value = {
      imageCount: 0,
      videoCount: 0,
      cameraCount: 0,
      totalCount: 0
    };
  }
};

// 根据路由参数设置默认标签页
const initializeFromRoute = () => {
  const type = route.query.type as string;
  if (type && ['image', 'video', 'camera'].includes(type)) {
    activeTab.value = type;
  }
};

onMounted(() => {
  initializeFromRoute();
  loadStatistics();
});
</script>

<style scoped lang="scss">
.records-management-container {
  .page-header {
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;

    .page-title {
      display: flex;
      align-items: center;
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;

      .el-icon {
        margin-right: 12px;
        font-size: 28px;
      }
    }

    .page-description {
      margin: 0;
      opacity: 0.9;
      font-size: 14px;
    }
  }

  .record-type-tabs {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;

    :deep(.el-tabs__header) {
      margin-bottom: 20px;
    }

    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 500;
    }

    .tab-content {
      min-height: 400px;
    }
  }

  .statistics-panel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }

      &.total {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;

        .stat-icon {
          background: rgba(255, 255, 255, 0.2);
        }
      }

      .stat-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: #f0f2f5;
        border-radius: 12px;
        margin-right: 16px;

        .el-icon {
          font-size: 24px;
          color: #667eea;
        }
      }

      .stat-content {
        .stat-number {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          opacity: 0.7;
        }
      }
    }
  }
}
</style>
