<template>
  <div class="camera-records">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input 
        v-model="searchParams.search1" 
        placeholder="请输入农作物类型" 
        style="max-width: 180px"
        clearable
      />
      <el-input 
        v-model="searchParams.search3" 
        placeholder="请输入最低阈值" 
        style="max-width: 180px; margin-left: 15px"
        clearable
      />
      <el-button type="primary" @click="loadData" class="ml10">
        <el-icon><Search /></el-icon>
        查询
      </el-button>
      <el-button @click="resetSearch" class="ml10">
        <el-icon><Refresh /></el-icon>
        重置
      </el-button>
    </div>

    <!-- 记录表格 -->
    <el-table :data="records" v-loading="loading" style="width: 100%">
      <el-table-column prop="num" label="序号" width="100" align="center" />
      <el-table-column prop="kind" label="农作物类型" width="120" align="center">
        <template #default="scope">
          <el-tag :type="getCropTypeTag(scope.row.kind)">{{ scope.row.kind }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="conf" label="置信度阈值" width="120" align="center">
        <template #default="scope">
          <span :class="getConfidenceClass(parseFloat(scope.row.conf) * 100)">
            {{ (parseFloat(scope.row.conf) * 100).toFixed(1) }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="username" label="用户" width="120" align="center" />
      <el-table-column prop="startTime" label="检测时间" width="180" align="center" />
      <el-table-column prop="weight" label="模型权重" width="150" align="center">
        <template #default="scope">
          <el-tag size="small">{{ scope.row.weight }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="检测结果" width="200" align="center">
        <template #default="scope">
          <el-button 
            type="success" 
            size="small" 
            @click="playVideo(scope.row.outVideo)"
            :disabled="!scope.row.outVideo"
          >
            <el-icon><VideoPlay /></el-icon>
            查看结果
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" align="center">
        <template #default="scope">
          <el-button 
            type="danger" 
            size="small" 
            @click="deleteRecord(scope.row)"
            plain
          >
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 视频播放对话框 -->
    <el-dialog v-model="videoDialogVisible" title="实时检测结果" width="70%">
      <div class="video-container">
        <video 
          v-if="currentVideoUrl" 
          :src="currentVideoUrl" 
          controls 
          class="video-player"
          @loadstart="onVideoLoadStart"
          @error="onVideoError"
        >
          您的浏览器不支持视频播放。
        </video>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Search, 
  Refresh, 
  Delete, 
  VideoPlay 
} from '@element-plus/icons-vue';
import request from '/@/utils/request';

interface Props {
  userFilter?: string;
}

const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);
const records = ref<any[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 搜索参数
const searchParams = reactive({
  search: '',
  search1: '',
  search3: '',
});

// 视频播放
const videoDialogVisible = ref(false);
const currentVideoUrl = ref('');

// 加载数据
const loadData = () => {
  loading.value = true;

  const params: any = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
  };

  // 如果有用户过滤，设置用户名参数
  if (props.userFilter) {
    params.username = props.userFilter;
  }

  // 设置搜索参数
  if (searchParams.search1) {
    params.cropType = searchParams.search1;
  }

  console.log('摄像头记录API请求参数:', params);

  request.get('/api/records/cameras', { params })
    .then((res) => {
      if (res.code === "0" || res.code === 0) {
        records.value = [];
        setTimeout(() => {
          loading.value = false;
        }, 500);

        for (let i = 0; i < res.data.records.length; i++) {
          const record = res.data.records[i];
          record.num = (currentPage.value - 1) * pageSize.value + i + 1;
          records.value[i] = record;
        }
        total.value = res.data.total;

        console.log('摄像头记录加载完成，记录数:', records.value.length);
      } else {
        loading.value = false;
        ElMessage.error(res.msg || '查询失败');
      }
    })
    .catch((error) => {
      loading.value = false;
      console.error('加载摄像头记录失败:', error);
      ElMessage.error('加载数据失败');
    });
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = '';
  });
  currentPage.value = 1;
  loadData();
};

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
  loadData();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  loadData();
};

// 删除记录
const deleteRecord = (record: any) => {
  ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    request.delete(`/api/records/cameras/${record.id}`)
      .then((res) => {
        if (res.code === "0" || res.code === 0) {
          ElMessage.success('删除成功');
          loadData();
        } else {
          ElMessage.error(res.msg || '删除失败');
        }
      })
      .catch((error) => {
        console.error('删除摄像头记录失败:', error);
        ElMessage.error('删除失败');
      });
  });
};

// 播放视频
const playVideo = (videoUrl: string) => {
  if (!videoUrl) {
    ElMessage.warning('视频文件不存在');
    return;
  }
  currentVideoUrl.value = videoUrl;
  videoDialogVisible.value = true;
};

// 视频加载事件
const onVideoLoadStart = () => {
  console.log('视频开始加载');
};

const onVideoError = () => {
  ElMessage.error('视频加载失败');
};

// 获取作物类型标签
const getCropTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    'corn': 'warning',
    'rice': 'success',
    'strawberry': 'danger',
    'tomato': 'info'
  };
  return typeMap[type] || '';
};

// 获取置信度样式类
const getConfidenceClass = (confidence: number) => {
  if (confidence >= 80) return 'high-confidence';
  if (confidence >= 60) return 'medium-confidence';
  return 'low-confidence';
};

// 监听用户过滤变化
watch(() => props.userFilter, () => {
  loadData();
});

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});

// 暴露刷新方法给父组件
defineExpose({
  refresh: loadData
});
</script>

<style scoped lang="scss">
.camera-records {
  .search-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .video-container {
    text-align: center;

    .video-player {
      width: 100%;
      max-height: 70vh;
      border-radius: 8px;
    }
  }

  :deep(.high-confidence) {
    color: #52c41a;
    font-weight: 600;
  }

  :deep(.medium-confidence) {
    color: #faad14;
    font-weight: 600;
  }

  :deep(.low-confidence) {
    color: #ff4d4f;
    font-weight: 600;
  }
}
</style>
