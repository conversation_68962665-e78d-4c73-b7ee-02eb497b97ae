<template>
  <div class="reports-page">
    <div class="page-header">
      <h1>智能分析报告</h1>
      <p>根据检测记录生成专业的农业分析报告</p>
    </div>

    <!-- 报告生成表单 -->
    <div class="report-form-section" v-if="!currentReport">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>生成分析报告</span>
          </div>
        </template>

        <el-form :model="reportForm" :rules="formRules" ref="formRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="作物类型" prop="cropType">
                <el-select v-model="reportForm.cropType" placeholder="请选择作物类型" style="width: 100%">
                  <el-option label="玉米" value="corn" />
                  <el-option label="水稻" value="rice" />
                  <el-option label="草莓" value="strawberry" />
                  <el-option label="西红柿" value="tomato" />
                  <el-option label="全部作物" value="" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报告类型" prop="reportType">
                <el-select v-model="reportForm.reportType" placeholder="请选择报告类型" style="width: 100%">
                  <el-option label="摘要报告" value="SUMMARY" />
                  <el-option label="详细报告" value="DETAILED" />
                  <el-option label="趋势分析" value="TREND" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="时间范围" prop="timeRange">
                <el-select v-model="reportForm.timeRange" placeholder="请选择时间范围" style="width: 100%">
                  <el-option label="今天" value="today" />
                  <el-option label="最近7天" value="week" />
                  <el-option label="最近30天" value="month" />
                  <el-option label="最近90天" value="quarter" />
                  <el-option label="自定义" value="custom" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="reportForm.timeRange === 'custom'">
              <el-form-item label="自定义时间">
                <el-date-picker
                  v-model="reportForm.customDateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="记录类型">
                <el-checkbox-group v-model="reportForm.recordTypes">
                  <el-checkbox label="IMG">图像检测</el-checkbox>
                  <el-checkbox label="VIDEO">视频检测</el-checkbox>
                  <el-checkbox label="CAMERA">实时检测</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分析维度">
                <el-checkbox-group v-model="reportForm.analysisDimensions">
                  <el-checkbox label="DISEASE">病害分析</el-checkbox>
                  <el-checkbox label="CONFIDENCE">置信度分析</el-checkbox>
                  <el-checkbox label="TIME">时间趋势</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="置信度范围">
            <el-slider
              v-model="reportForm.confidenceRange"
              range
              :min="0"
              :max="100"
              :step="5"
              show-stops
              :format-tooltip="(val) => `${val}%`"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="generateReport" :loading="generating">
              <el-icon><Document /></el-icon>
              生成报告
            </el-button>
            <el-button @click="resetForm">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 报告展示区域 -->
    <div class="report-display-section" v-if="currentReport">
      <div class="report-actions">
        <el-button @click="backToForm">
          <el-icon><ArrowLeft /></el-icon>
          返回生成
        </el-button>
        <el-button type="primary" @click="regenerateReport" :loading="generating">
          <el-icon><Refresh /></el-icon>
          重新生成
        </el-button>
      </div>

      <ReportViewer :report="currentReport" ref="reportViewerRef" />
    </div>

    <!-- 历史报告 -->
    <div class="history-section" v-if="!currentReport && reportHistory.length > 0">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>历史报告</span>
            <el-button type="text" @click="clearHistory">清空历史</el-button>
          </div>
        </template>

        <el-table :data="reportHistory" stripe>
          <el-table-column prop="title" label="报告标题" show-overflow-tooltip />
          <el-table-column prop="cropType" label="作物类型" width="100" />
          <el-table-column prop="reportType" label="报告类型" width="100" />
          <el-table-column prop="generateTime" label="生成时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.generateTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalRecords" label="记录数" width="100" />
          <el-table-column label="操作" width="150">
            <template #default="{ row, $index }">
              <el-button type="text" @click="viewReport(row)">查看</el-button>
              <el-button type="text" @click="deleteReport($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Refresh, ArrowLeft } from '@element-plus/icons-vue'
import ReportViewer from '/@/components/ReportViewer.vue'
import { chatApi } from '/@/api/chat'

// 组件引用
const formRef = ref()
const reportViewerRef = ref()

// 状态
const generating = ref(false)
const currentReport = ref(null)
const reportHistory = ref([])

// 表单数据
const reportForm = reactive({
  cropType: '',
  reportType: 'SUMMARY',
  timeRange: 'week',
  customDateRange: [],
  recordTypes: ['IMG'],
  analysisDimensions: ['DISEASE', 'CONFIDENCE', 'TIME'],
  confidenceRange: [0, 100]
})

// 表单验证规则
const formRules = {
  reportType: [
    { required: true, message: '请选择报告类型', trigger: 'change' }
  ],
  timeRange: [
    { required: true, message: '请选择时间范围', trigger: 'change' }
  ]
}

// 方法
const generateReport = async () => {
  try {
    await formRef.value.validate()
    
    generating.value = true
    
    // 构建查询指令
    let command = '生成'
    
    // 添加报告类型
    if (reportForm.reportType === 'DETAILED') {
      command += '详细'
    } else if (reportForm.reportType === 'TREND') {
      command += '趋势'
    }
    command += '分析报告'
    
    // 添加作物类型
    if (reportForm.cropType) {
      const cropNames = {
        corn: '玉米',
        rice: '水稻',
        strawberry: '草莓',
        tomato: '西红柿'
      }
      command += `，作物类型：${cropNames[reportForm.cropType]}`
    }
    
    // 添加时间范围
    if (reportForm.timeRange === 'today') {
      command += '，时间范围：今天'
    } else if (reportForm.timeRange === 'week') {
      command += '，时间范围：最近7天'
    } else if (reportForm.timeRange === 'month') {
      command += '，时间范围：最近30天'
    } else if (reportForm.timeRange === 'quarter') {
      command += '，时间范围：最近90天'
    } else if (reportForm.timeRange === 'custom' && reportForm.customDateRange.length === 2) {
      const startDate = reportForm.customDateRange[0].toLocaleDateString()
      const endDate = reportForm.customDateRange[1].toLocaleDateString()
      command += `，时间范围：${startDate}到${endDate}`
    }
    
    // 添加置信度范围
    if (reportForm.confidenceRange[0] > 0 || reportForm.confidenceRange[1] < 100) {
      command += `，置信度范围：${reportForm.confidenceRange[0]}%到${reportForm.confidenceRange[1]}%`
    }
    
    console.log('生成报告指令:', command)
    
    // 调用聊天API生成报告
    const response = await chatApi.sendMessage(command)
    
    if (response.data && response.data.reportData) {
      currentReport.value = response.data.reportData
      
      // 保存到历史记录
      const historyItem = {
        ...response.data.reportData,
        generateTime: new Date().toISOString()
      }
      reportHistory.value.unshift(historyItem)
      
      // 限制历史记录数量
      if (reportHistory.value.length > 10) {
        reportHistory.value = reportHistory.value.slice(0, 10)
      }
      
      // 保存到本地存储
      localStorage.setItem('reportHistory', JSON.stringify(reportHistory.value))
      
      ElMessage.success('报告生成成功')
    } else {
      ElMessage.error('报告生成失败，请重试')
    }
    
  } catch (error) {
    console.error('生成报告失败:', error)
    ElMessage.error('生成报告失败: ' + (error.message || '未知错误'))
  } finally {
    generating.value = false
  }
}

const resetForm = () => {
  formRef.value.resetFields()
  reportForm.recordTypes = ['IMG']
  reportForm.analysisDimensions = ['DISEASE', 'CONFIDENCE', 'TIME']
  reportForm.confidenceRange = [0, 100]
}

const backToForm = () => {
  currentReport.value = null
  if (reportViewerRef.value) {
    reportViewerRef.value.cleanup()
  }
}

const regenerateReport = () => {
  currentReport.value = null
  generateReport()
}

const viewReport = (report) => {
  currentReport.value = report
}

const deleteReport = async (index) => {
  try {
    await ElMessageBox.confirm('确定要删除这个报告吗？', '确认删除', {
      type: 'warning'
    })
    
    reportHistory.value.splice(index, 1)
    localStorage.setItem('reportHistory', JSON.stringify(reportHistory.value))
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const clearHistory = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有历史报告吗？', '确认清空', {
      type: 'warning'
    })
    
    reportHistory.value = []
    localStorage.removeItem('reportHistory')
    ElMessage.success('清空成功')
  } catch {
    // 用户取消清空
  }
}

const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 检查是否有来自聊天的报告数据
  const chatReportData = sessionStorage.getItem('currentReport')
  if (chatReportData) {
    try {
      const reportData = JSON.parse(chatReportData)
      console.log('从聊天加载报告数据:', reportData)
      currentReport.value = reportData

      // 清除sessionStorage中的数据
      sessionStorage.removeItem('currentReport')

      // 保存到历史记录
      const historyItem = {
        ...reportData,
        generateTime: reportData.generateTime || new Date().toISOString()
      }
      reportHistory.value.unshift(historyItem)

      // 限制历史记录数量
      if (reportHistory.value.length > 10) {
        reportHistory.value = reportHistory.value.slice(0, 10)
      }

      // 保存到本地存储
      localStorage.setItem('reportHistory', JSON.stringify(reportHistory.value))

    } catch (error) {
      console.error('解析聊天报告数据失败:', error)
    }
  }

  // 加载历史记录
  const savedHistory = localStorage.getItem('reportHistory')
  if (savedHistory) {
    try {
      reportHistory.value = JSON.parse(savedHistory)
    } catch (error) {
      console.error('加载历史记录失败:', error)
    }
  }
})
</script>

<style scoped lang="scss">
.reports-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);

  .page-header {
    text-align: center;
    margin-bottom: 30px;

    h1 {
      font-size: 32px;
      color: #303133;
      margin-bottom: 10px;
    }

    p {
      font-size: 16px;
      color: #909399;
    }
  }

  .report-form-section {
    margin-bottom: 30px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 16px;
    }

    .el-form {
      .el-form-item {
        margin-bottom: 20px;
      }

      .el-slider {
        margin: 0 20px;
      }
    }
  }

  .report-display-section {
    .report-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 15px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .history-section {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 16px;
    }

    .el-table {
      .el-button--text {
        padding: 0;
        margin-right: 10px;
      }
    }
  }

  .el-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-card__header) {
      background: #fafafa;
      border-bottom: 1px solid #e4e7ed;
    }
  }

  .el-button {
    border-radius: 6px;

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      }
    }
  }

  .el-select,
  .el-date-picker {
    .el-input__inner {
      border-radius: 6px;
    }
  }

  .el-checkbox-group {
    .el-checkbox {
      margin-right: 20px;
      margin-bottom: 10px;
    }
  }

  .el-slider {
    .el-slider__runway {
      background: #e4e7ed;
    }

    .el-slider__bar {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .el-slider__button {
      border: 2px solid #667eea;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .reports-page {
    padding: 10px;

    .page-header {
      h1 {
        font-size: 24px;
      }

      p {
        font-size: 14px;
      }
    }

    .el-row {
      .el-col {
        margin-bottom: 10px;
      }
    }

    .report-actions {
      flex-direction: column;
      gap: 10px;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
