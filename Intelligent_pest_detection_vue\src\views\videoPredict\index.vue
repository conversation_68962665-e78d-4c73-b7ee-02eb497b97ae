<template>
	<div class="pest-page-container">
		<div class="pest-content-wrapper">
			<!-- 页面标题 -->
			<div class="page-header animate-fade-in-up">
				<h1 class="pest-title">视频害虫检测</h1>
				<p class="pest-subtitle">上传视频文件，使用YOLO11算法批量检测视频中的害虫</p>
			</div>

			<!-- 控制面板 -->
			<div class="control-panel pest-card animate-fade-in-up animate-delay-100">
				<div class="control-header">
					<i class="iconfont icon-shezhi"></i>
					<span>检测参数设置</span>
				</div>

				<div class="control-content">
					<div class="control-row">
						<div class="control-item">
							<label class="control-label">作物种类</label>
							<el-select
								v-model="kind"
								placeholder="请选择作物种类"
								size="large"
								class="control-select"
								@change="getData"
							>
								<el-option
									v-for="item in state.kind_items"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>

						<div class="control-item">
							<label class="control-label">检测模型</label>
							<el-select
								v-model="weight"
								placeholder="请选择模型"
								size="large"
								class="control-select"
							>
								<el-option
									v-for="item in state.weight_items"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>

						<div class="control-item confidence-item">
							<label class="control-label">置信度阈值</label>
							<div class="confidence-slider">
								<el-slider
									v-model="conf"
									:format-tooltip="formatTooltip"
									:show-tooltip="true"
									class="slider"
								/>
								<span class="confidence-value">{{ formatTooltip(conf) }}</span>
							</div>
						</div>
					</div>

					<div class="action-row">
						<el-upload
							v-model="state.form.inputVideo"
							ref="uploadFile"
							class="video-uploader"
							action="http://localhost:9999/files/upload"
							:show-file-list="false"
							:on-success="handleAvatarSuccessone"
							accept="video/*"
						>
							<el-button
								type="info"
								class="pest-btn-secondary upload-button"
								size="large"
							>
								<i class="iconfont icon-shangchuan" style="margin-right: 8px;"></i>
								上传视频
							</el-button>
						</el-upload>

						<el-button
							type="primary"
							@click="upData"
							class="pest-btn-primary process-button"
							size="large"
							:disabled="!state.form.inputVideo || !weight || !kind"
						>
							<i class="iconfont icon-shibie" style="margin-right: 8px;"></i>
							开始处理
						</el-button>
					</div>

					<!-- 进度条 -->
					<div class="progress-section" v-if="state.isShow">
						<div class="progress-info">
							<i class="iconfont icon-jiazai"></i>
							<span>{{ state.type_text }}</span>
						</div>
						<el-progress
							:text-inside="true"
							:stroke-width="24"
							:percentage="state.percentage"
							class="progress-bar"
						>
							<span>{{ state.percentage }}%</span>
						</el-progress>
					</div>
				</div>
			</div>

			<!-- 视频显示区域 -->
			<div class="video-display pest-card animate-fade-in-up animate-delay-200" v-if="state.video_path">
				<div class="video-header">
					<i class="iconfont icon-shipin1"></i>
					<span>检测结果视频</span>
				</div>

				<div class="video-container">
					<img :src="state.video_path" class="result-video" />
				</div>
			</div>

			<!-- 视频上传提示 -->
			<div class="upload-tips pest-card animate-fade-in-up animate-delay-300" v-else>
				<div class="tips-content">
					<i class="iconfont icon-shipin1"></i>
					<h3>视频检测说明</h3>
					<p>请上传需要检测的视频文件，系统将逐帧分析并标注检测到的害虫</p>
					<div class="tips-list">
						<div class="tip-item">
							<i class="iconfont icon-zhengque"></i>
							<span>支持 MP4、AVI、MOV 等常见视频格式</span>
						</div>
						<div class="tip-item">
							<i class="iconfont icon-zhengque"></i>
							<span>建议视频清晰度不低于720P</span>
						</div>
						<div class="tip-item">
							<i class="iconfont icon-zhengque"></i>
							<span>处理时间取决于视频长度和分辨率</span>
						</div>
						<div class="tip-item">
							<i class="iconfont icon-zhengque"></i>
							<span>检测完成后可下载标注后的视频</span>
						</div>
					</div>
				</div>
			</div>

			<!-- 功能特色 -->
			<div class="feature-highlights pest-card animate-fade-in-up animate-delay-400">
				<div class="highlights-header">
					<i class="iconfont icon-tese"></i>
					<span>视频检测特色</span>
				</div>
				<div class="highlights-grid">
					<div class="highlight-item">
						<i class="iconfont icon-gaoxiao"></i>
						<h4>高效处理</h4>
						<p>采用GPU加速，快速处理大容量视频文件</p>
					</div>
					<div class="highlight-item">
						<i class="iconfont icon-zhunque"></i>
						<h4>精准识别</h4>
						<p>YOLO11算法确保高精度害虫检测</p>
					</div>
					<div class="highlight-item">
						<i class="iconfont icon-shishi"></i>
						<h4>实时反馈</h4>
						<p>处理过程实时显示进度和状态</p>
					</div>
					<div class="highlight-item">
						<i class="iconfont icon-baocun"></i>
						<h4>结果保存</h4>
						<p>自动保存检测结果和标注视频</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>


<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import request from '/@/utils/request';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
import type { UploadInstance, UploadProps } from 'element-plus';
import { SocketService } from '/@/utils/socket';
import { formatDate } from '/@/utils/formatTime';

const uploadFile = ref<UploadInstance>();
const stores = useUserInfo();
const conf = ref('');
const kind = ref('');
const weight = ref('');
const { userInfos } = storeToRefs(stores);

const handleAvatarSuccessone: UploadProps['onSuccess'] = (response, uploadFile) => {
	ElMessage.success('上传成功！');
	state.form.inputVideo = response.data;
};
const state = reactive({
	weight_items: [] as any,
	kind_items: [
		{
			value: 'corn',
			label: '玉米',
		},
		{
			value: 'rice',
			label: '水稻',
		},
		{
			value: 'strawberry',
			label: '草莓',
		},
		{
			value: 'tomato',
			label: '西红柿',
		},
	],
	data: {} as any,
	video_path: '',
	type_text: "正在保存",
	percentage: 50,
	isShow: false,
	form: {
		username: '',
		inputVideo: null as any,
		weight: '',
		conf: null as any,
		kind: '',
		startTime: ''
	},
});

const socketService = new SocketService();

socketService.on('message', (data) => {
	console.log('Received message:', data);
	ElMessage.success(data);
});

const formatTooltip = (val: number) => {
	return val / 100
}

socketService.on('progress', (data) => {
	state.percentage = parseInt(data);
	if (parseInt(data) < 100) {
		state.isShow = true;
	} else {
		//两秒后隐藏进度条
		ElMessage.success("保存成功！");
		setTimeout(() => {
			state.isShow = false;
			state.percentage = 0;
		}, 2000);
	}
	console.log('Received message:', data);
});

const getData = () => {
	request.get('/api/flask/file_names').then((res) => {
		if (res.code == 0) {
			res.data = JSON.parse(res.data);
			state.weight_items = res.data.weight_items.filter(item => item.value.includes(kind.value));
		} else {
			ElMessage.error(res.msg);
		}
	});
};


const upData = () => {
	state.form.weight = weight.value;
	state.form.conf = (parseFloat(conf.value)/100);
	state.form.username = userInfos.value.userName;
	state.form.kind = kind.value;
	state.form.startTime = formatDate(new Date(), 'YYYY-mm-dd HH:MM:SS');
	console.log(state.form);
	const queryParams = new URLSearchParams(state.form).toString();
	state.video_path = `http://127.0.0.1:5000/predictVideo?${queryParams}`;
	ElMessage.success('正在加载！');
};

onMounted(() => {
	getData();
});
</script>

<style scoped lang="scss">
.page-header {
	text-align: center;
	margin-bottom: var(--spacing-xl);
}

.control-panel {
	margin-bottom: var(--spacing-xl);

	.control-header {
		display: flex;
		align-items: center;
		gap: var(--spacing-sm);
		margin-bottom: var(--spacing-lg);
		font-size: 1.125rem;
		font-weight: 600;
		color: var(--text-primary);

		i {
			color: var(--primary-color);
			font-size: 1.25rem;
		}
	}

	.control-content {
		.control-row {
			display: grid;
			grid-template-columns: 1fr 1fr 2fr;
			gap: var(--spacing-lg);
			align-items: end;
			margin-bottom: var(--spacing-lg);
		}

		.control-item {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-sm);

			.control-label {
				font-size: 0.875rem;
				font-weight: 500;
				color: var(--text-secondary);
			}

			.control-select {
				width: 100%;
			}
		}

		.confidence-item {
			.confidence-slider {
				display: flex;
				align-items: center;
				gap: var(--spacing-md);

				.slider {
					flex: 1;
				}

				.confidence-value {
					min-width: 40px;
					text-align: center;
					font-weight: 600;
					color: var(--primary-color);
				}
			}
		}

		.action-row {
			display: flex;
			gap: var(--spacing-lg);
			justify-content: center;
			margin-bottom: var(--spacing-lg);

			.upload-button, .process-button {
				height: 48px;
				padding: 0 var(--spacing-xl);
				font-weight: 600;
			}
		}

		.progress-section {
			.progress-info {
				display: flex;
				align-items: center;
				gap: var(--spacing-sm);
				margin-bottom: var(--spacing-md);
				font-weight: 500;
				color: var(--text-secondary);

				i {
					color: var(--primary-color);
					animation: spin 1s linear infinite;
				}
			}

			.progress-bar {
				:deep(.el-progress-bar__outer) {
					background: var(--bg-secondary);
				}

				:deep(.el-progress-bar__inner) {
					background: var(--primary-gradient);
				}
			}
		}
	}
}

.video-display {
	margin-bottom: var(--spacing-xl);

	.video-header {
		display: flex;
		align-items: center;
		gap: var(--spacing-sm);
		margin-bottom: var(--spacing-lg);
		font-size: 1.125rem;
		font-weight: 600;
		color: var(--text-primary);

		i {
			color: var(--primary-color);
			font-size: 1.25rem;
		}
	}

	.video-container {
		width: 100%;
		border-radius: var(--radius-lg);
		overflow: hidden;
		background: var(--bg-secondary);

		.result-video {
			width: 100%;
			height: auto;
			max-height: 600px;
			object-fit: contain;
			display: block;
		}
	}
}

.upload-tips {
	margin-bottom: var(--spacing-xl);

	.tips-content {
		text-align: center;
		padding: var(--spacing-2xl);

		> i {
			font-size: 4rem;
			color: var(--text-muted);
			margin-bottom: var(--spacing-lg);
		}

		h3 {
			font-size: 1.5rem;
			font-weight: 600;
			color: var(--text-primary);
			margin-bottom: var(--spacing-md);
		}

		> p {
			color: var(--text-secondary);
			font-size: 1rem;
			margin-bottom: var(--spacing-xl);
			line-height: 1.6;
		}

		.tips-list {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: var(--spacing-md);
			max-width: 600px;
			margin: 0 auto;

			.tip-item {
				display: flex;
				align-items: center;
				gap: var(--spacing-sm);
				text-align: left;

				i {
					color: var(--secondary-color);
					font-size: 1rem;
				}

				span {
					color: var(--text-secondary);
					font-size: 0.875rem;
				}
			}
		}
	}
}

.feature-highlights {
	.highlights-header {
		display: flex;
		align-items: center;
		gap: var(--spacing-sm);
		margin-bottom: var(--spacing-lg);
		font-size: 1.125rem;
		font-weight: 600;
		color: var(--text-primary);

		i {
			color: var(--secondary-color);
			font-size: 1.25rem;
		}
	}

	.highlights-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: var(--spacing-lg);

		.highlight-item {
			text-align: center;
			padding: var(--spacing-lg);
			background: var(--bg-secondary);
			border-radius: var(--radius-md);
			transition: all var(--transition-normal);

			&:hover {
				transform: translateY(-4px);
				box-shadow: var(--shadow-md);
			}

			i {
				font-size: 2.5rem;
				color: var(--secondary-color);
				margin-bottom: var(--spacing-md);
			}

			h4 {
				font-size: 1rem;
				font-weight: 600;
				color: var(--text-primary);
				margin-bottom: var(--spacing-sm);
			}

			p {
				font-size: 0.875rem;
				color: var(--text-secondary);
				line-height: 1.5;
			}
		}
	}
}

// 动画效果
@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

// 响应式设计
@media (max-width: 1024px) {
	.control-content .control-row {
		grid-template-columns: 1fr 1fr;

		.confidence-item {
			grid-column: 1 / -1;
		}
	}

	.highlights-grid {
		grid-template-columns: repeat(2, 1fr);
	}

	.tips-list {
		grid-template-columns: 1fr;
	}
}

@media (max-width: 768px) {
	.control-content .control-row {
		grid-template-columns: 1fr;
	}

	.action-row {
		flex-direction: column;
		align-items: center;
	}

	.highlights-grid {
		grid-template-columns: 1fr;
	}
}
</style>