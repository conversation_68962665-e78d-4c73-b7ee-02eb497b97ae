<template>
  <div class="record-operation-display" v-if="result">
    <!-- 成功操作结果 -->
    <div v-if="result.success" class="operation-success">
      <div class="operation-header">
        <i class="iconfont icon-chenggong success-icon"></i>
        <span class="operation-type">{{ getOperationTypeName(result.operationType) }}</span>
        <span class="operation-status">成功</span>
      </div>
      
      <!-- 查询结果展示 -->
      <div v-if="result.operationType === 'QUERY'" class="query-result">
        <div class="result-summary">
          <div class="summary-item">
            <span class="label">总记录数：</span>
            <span class="value">{{ result.totalCount || 0 }} 条</span>
          </div>
          <div v-if="result.currentPage && result.totalPages" class="summary-item">
            <span class="label">当前页：</span>
            <span class="value">第 {{ result.currentPage }} 页，共 {{ result.totalPages }} 页</span>
          </div>
        </div>
        
        <!-- 统计信息 -->
        <div v-if="result.statistics" class="statistics">
          <div class="stat-item" v-if="result.statistics.imageCount !== undefined">
            <i class="iconfont icon-tupian"></i>
            <span>图像检测：{{ result.statistics.imageCount }} 条</span>
          </div>
          <div class="stat-item" v-if="result.statistics.videoCount !== undefined">
            <i class="iconfont icon-shipin"></i>
            <span>视频检测：{{ result.statistics.videoCount }} 条</span>
          </div>
          <div class="stat-item" v-if="result.statistics.cameraCount !== undefined">
            <i class="iconfont icon-shexiangtou"></i>
            <span>摄像头检测：{{ result.statistics.cameraCount }} 条</span>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button size="small" type="primary" @click="viewDetails">
            <i class="iconfont icon-chakan"></i>
            查看详情
          </el-button>
          <el-button size="small" @click="exportData">
            <i class="iconfont icon-daochu"></i>
            导出数据
          </el-button>
        </div>
      </div>
      
      <!-- 统计结果展示 -->
      <div v-else-if="result.operationType === 'COUNT'" class="count-result">
        <div class="count-display">
          <div class="count-number">{{ result.totalCount || 0 }}</div>
          <div class="count-label">条记录</div>
        </div>
        
        <!-- 详细统计 -->
        <div v-if="result.statistics" class="detailed-stats">
          <div class="stat-row" v-if="result.statistics.imageCount !== undefined">
            <span class="stat-label">图像检测：</span>
            <span class="stat-value">{{ result.statistics.imageCount }}</span>
          </div>
          <div class="stat-row" v-if="result.statistics.videoCount !== undefined">
            <span class="stat-label">视频检测：</span>
            <span class="stat-value">{{ result.statistics.videoCount }}</span>
          </div>
          <div class="stat-row" v-if="result.statistics.cameraCount !== undefined">
            <span class="stat-label">摄像头检测：</span>
            <span class="stat-value">{{ result.statistics.cameraCount }}</span>
          </div>
        </div>
      </div>
      
      <!-- 删除结果展示 -->
      <div v-else-if="result.operationType === 'DELETE'" class="delete-result">
        <div class="delete-summary">
          <i class="iconfont icon-shanchu delete-icon"></i>
          <span class="delete-count">已删除 {{ result.affectedCount || 0 }} 条记录</span>
        </div>
        
        <!-- 删除详情 -->
        <div v-if="result.details && result.details.length > 0" class="delete-details">
          <div class="detail-item" v-for="detail in result.details" :key="detail">
            <i class="iconfont icon-dian"></i>
            <span>{{ detail }}</span>
          </div>
        </div>
        
        <div class="delete-warning">
          <i class="iconfont icon-jinggao"></i>
          <span>删除操作不可撤销</span>
        </div>
      </div>
      
      <!-- 执行时间 -->
      <div v-if="result.executionTime" class="execution-time">
        <i class="iconfont icon-shijian"></i>
        <span>执行时间：{{ result.executionTime }}ms</span>
      </div>
    </div>
    
    <!-- 需要确认的操作 -->
    <div v-else-if="result.needConfirmation" class="operation-confirmation">
      <div class="confirmation-header">
        <i class="iconfont icon-jinggao warning-icon"></i>
        <span class="confirmation-title">需要确认</span>
      </div>
      
      <div class="confirmation-message">
        {{ result.confirmationMessage }}
      </div>
      
      <div class="confirmation-info" v-if="result.affectedCount">
        <span>将影响 {{ result.affectedCount }} 条记录</span>
      </div>
      
      <div class="confirmation-buttons">
        <el-button size="small" type="danger" @click="confirmOperation">
          <i class="iconfont icon-queren"></i>
          确认执行
        </el-button>
        <el-button size="small" @click="cancelOperation">
          <i class="iconfont icon-quxiao"></i>
          取消
        </el-button>
      </div>
    </div>
    
    <!-- 操作失败 -->
    <div v-else class="operation-error">
      <div class="error-header">
        <i class="iconfont icon-cuowu error-icon"></i>
        <span class="error-title">操作失败</span>
      </div>
      
      <div class="error-message">
        {{ result.error }}
      </div>
      
      <!-- 建议 -->
      <div v-if="result.suggestions && result.suggestions.length > 0" class="error-suggestions">
        <div class="suggestions-title">建议：</div>
        <div class="suggestion-item" v-for="suggestion in result.suggestions" :key="suggestion">
          <i class="iconfont icon-dian"></i>
          <span>{{ suggestion }}</span>
        </div>
      </div>
      
      <div class="error-actions">
        <el-button size="small" type="primary" @click="retryOperation">
          <i class="iconfont icon-chongshi"></i>
          重试
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElButton } from 'element-plus';
import { useRouter } from 'vue-router';
import { useChatStore } from '/@/stores/chat';

interface RecordOperationResult {
  success: boolean;
  operationType: string;
  recordType: string;
  message: string;
  error?: string;
  data?: any;
  totalCount?: number;
  currentPageCount?: number;
  totalPages?: number;
  currentPage?: number;
  affectedCount?: number;
  statistics?: Record<string, any>;
  details?: string[];
  needConfirmation?: boolean;
  confirmationMessage?: string;
  suggestions?: string[];
  executionTime?: number;
}

interface Props {
  result: RecordOperationResult;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  sendMessage: [message: string];
}>();
const router = useRouter();

// 获取操作类型名称
const getOperationTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'QUERY': '查询',
    'COUNT': '统计',
    'DELETE': '删除',
    'EXPORT': '导出'
  };
  return typeMap[type] || type;
};

// 查看详情
const viewDetails = () => {
  // 根据记录类型跳转到对应的记录管理页面
  const recordType = props.result.recordType;
  let targetRoute = '/records/management';
  let queryParams: any = {};

  if (recordType === 'IMG') {
    queryParams.type = 'image';
  } else if (recordType === 'VIDEO') {
    queryParams.type = 'video';
  } else if (recordType === 'CAMERA') {
    queryParams.type = 'camera';
  }

  // 从聊天store获取当前查询的用户名
  const chatStore = useChatStore();
  if (chatStore.currentSessionUser) {
    queryParams.user = chatStore.currentSessionUser;
  }

  router.push({
    path: targetRoute,
    query: queryParams
  });
  ElMessage.success('正在跳转到记录管理页面');
};

// 导出数据
const exportData = () => {
  ElMessage.info('导出功能开发中...');
};

// 确认操作
const confirmOperation = () => {
  emit('sendMessage', '确认删除');
  ElMessage.success('已发送确认指令');
};

// 取消操作
const cancelOperation = () => {
  emit('sendMessage', '取消删除');
  ElMessage.info('已取消操作');
};

// 重试操作
const retryOperation = () => {
  ElMessage.info('请重新输入指令或检查参数');
};
</script>

<style scoped lang="scss">
.record-operation-display {
  margin-top: 8px;
  border-radius: 8px;
  overflow: hidden;
  font-size: 13px;
  
  .operation-success {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #0ea5e9;
    
    .operation-header {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background: rgba(14, 165, 233, 0.1);
      border-bottom: 1px solid rgba(14, 165, 233, 0.2);
      
      .success-icon {
        color: #10b981;
        margin-right: 6px;
        font-size: 14px;
      }
      
      .operation-type {
        font-weight: 600;
        color: #0ea5e9;
        margin-right: 8px;
      }
      
      .operation-status {
        color: #10b981;
        font-size: 12px;
      }
    }
    
    .query-result, .count-result, .delete-result {
      padding: 12px;
    }
    
    .result-summary {
      display: flex;
      gap: 16px;
      margin-bottom: 8px;
      
      .summary-item {
        .label {
          color: #64748b;
          font-size: 12px;
        }
        
        .value {
          color: #0f172a;
          font-weight: 600;
        }
      }
    }
    
    .statistics {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 12px;
      
      .stat-item {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 4px;
        font-size: 12px;
        
        i {
          margin-right: 4px;
          color: #0ea5e9;
        }
      }
    }
    
    .count-display {
      text-align: center;
      margin-bottom: 12px;
      
      .count-number {
        font-size: 24px;
        font-weight: bold;
        color: #0ea5e9;
      }
      
      .count-label {
        font-size: 12px;
        color: #64748b;
      }
    }
    
    .detailed-stats {
      .stat-row {
        display: flex;
        justify-content: space-between;
        padding: 2px 0;
        
        .stat-label {
          color: #64748b;
          font-size: 12px;
        }
        
        .stat-value {
          color: #0f172a;
          font-weight: 600;
        }
      }
    }
    
    .delete-summary {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .delete-icon {
        color: #ef4444;
        margin-right: 6px;
      }
      
      .delete-count {
        font-weight: 600;
        color: #ef4444;
      }
    }
    
    .delete-details {
      margin-bottom: 8px;
      
      .detail-item {
        display: flex;
        align-items: center;
        padding: 2px 0;
        font-size: 12px;
        color: #64748b;
        
        i {
          margin-right: 4px;
          font-size: 8px;
        }
      }
    }
    
    .delete-warning {
      display: flex;
      align-items: center;
      padding: 6px 8px;
      background: rgba(239, 68, 68, 0.1);
      border-radius: 4px;
      color: #ef4444;
      font-size: 12px;
      
      i {
        margin-right: 4px;
      }
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
      margin-top: 12px;
    }
    
    .execution-time {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid rgba(14, 165, 233, 0.2);
      color: #64748b;
      font-size: 11px;
      
      i {
        margin-right: 4px;
      }
    }
  }
  
  .operation-confirmation {
    background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
    border: 1px solid #f59e0b;
    padding: 12px;
    
    .confirmation-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .warning-icon {
        color: #f59e0b;
        margin-right: 6px;
        font-size: 14px;
      }
      
      .confirmation-title {
        font-weight: 600;
        color: #f59e0b;
      }
    }
    
    .confirmation-message {
      color: #92400e;
      margin-bottom: 8px;
      line-height: 1.4;
    }
    
    .confirmation-info {
      color: #d97706;
      font-size: 12px;
      margin-bottom: 12px;
    }
    
    .confirmation-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .operation-error {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border: 1px solid #ef4444;
    padding: 12px;
    
    .error-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .error-icon {
        color: #ef4444;
        margin-right: 6px;
        font-size: 14px;
      }
      
      .error-title {
        font-weight: 600;
        color: #ef4444;
      }
    }
    
    .error-message {
      color: #dc2626;
      margin-bottom: 8px;
      line-height: 1.4;
    }
    
    .error-suggestions {
      margin-bottom: 12px;
      
      .suggestions-title {
        color: #991b1b;
        font-weight: 600;
        margin-bottom: 4px;
        font-size: 12px;
      }
      
      .suggestion-item {
        display: flex;
        align-items: center;
        padding: 2px 0;
        font-size: 12px;
        color: #dc2626;
        
        i {
          margin-right: 4px;
          font-size: 8px;
        }
      }
    }
    
    .error-actions {
      display: flex;
      gap: 8px;
    }
  }
}
</style>
