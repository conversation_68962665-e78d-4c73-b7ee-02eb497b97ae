<template>
  <div class="image-records">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input 
        v-model="searchParams.search1" 
        placeholder="请输入农作物类型" 
        style="max-width: 180px"
        clearable
      />
      <el-input 
        v-model="searchParams.search2" 
        placeholder="请输入病害名称" 
        style="max-width: 180px; margin-left: 15px"
        clearable
      />
      <el-input 
        v-model="searchParams.search3" 
        placeholder="请输入最低阈值" 
        style="max-width: 180px; margin-left: 15px"
        clearable
      />
      <el-button type="primary" @click="loadData" class="ml10">
        <el-icon><Search /></el-icon>
        查询
      </el-button>
      <el-button @click="resetSearch" class="ml10">
        <el-icon><Refresh /></el-icon>
        重置
      </el-button>
    </div>

    <!-- 记录列表 -->
    <div class="records-grid" v-loading="loading">
      <div
        v-for="(record, index) in records"
        :key="record.id"
        class="record-card"
        :style="{ animationDelay: `${0.1 * index}s` }"
      >
        <div class="record-header">
          <div class="record-number">
            <span class="number-label">#</span>
            <span class="number-value">{{ record.num }}</span>
          </div>
          <div class="record-actions">
            <el-button type="danger" size="small" @click="deleteRecord(record)" plain>
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>

        <div class="record-content">
          <!-- 图片展示 -->
          <div class="image-section">
            <div class="image-container">
              <img 
                :src="record.inputImg" 
                alt="输入图像" 
                class="input-image"
                @click="previewImage(record.inputImg)"
              />
              <div class="image-label">输入图像</div>
            </div>
            <div class="arrow-icon">
              <el-icon><ArrowRight /></el-icon>
            </div>
            <div class="image-container">
              <img 
                :src="record.outImg" 
                alt="检测结果" 
                class="output-image"
                @click="previewImage(record.outImg)"
              />
              <div class="image-label">检测结果</div>
            </div>
          </div>

          <!-- 检测信息 -->
          <div class="detection-info">
            <div class="info-row">
              <span class="label">作物类型：</span>
              <span class="value crop-type">{{ record.kind }}</span>
            </div>
            <div class="info-row">
              <span class="label">检测时间：</span>
              <span class="value">{{ record.startTime }}</span>
            </div>
            <div class="info-row">
              <span class="label">用户：</span>
              <span class="value">{{ record.username }}</span>
            </div>
            <div class="info-row">
              <span class="label">处理时间：</span>
              <span class="value">{{ record.allTime }}</span>
            </div>
          </div>

          <!-- 检测结果 -->
          <div class="detection-results">
            <div class="results-header">
              <el-icon><Document /></el-icon>
              <span>检测结果</span>
            </div>
            <div class="results-list">
              <div 
                v-for="(result, idx) in record.results" 
                :key="idx"
                class="result-item"
              >
                <div class="result-label">{{ result.label }}</div>
                <div class="result-confidence" :class="getConfidenceClass(result.confidence)">
                  {{ result.confidence }}%
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 图片预览 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="60%">
      <div class="preview-container">
        <img :src="previewUrl" alt="预览图片" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Search, 
  Refresh, 
  Delete, 
  ArrowRight, 
  Document 
} from '@element-plus/icons-vue';
import request from '/@/utils/request';

interface Props {
  userFilter?: string;
}

const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);
const records = ref<any[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 搜索参数
const searchParams = reactive({
  search: '',
  search1: '',
  search2: '',
  search3: '',
});

// 图片预览
const previewVisible = ref(false);
const previewUrl = ref('');

// 数据转换函数
const transformData = (record: any, confidences: string[], labels: string[]) => {
  const results = [];
  for (let i = 0; i < Math.min(confidences.length, labels.length); i++) {
    results.push({
      label: labels[i],
      confidence: parseFloat(confidences[i].replace('%', '')).toFixed(2)
    });
  }
  return {
    ...record,
    results
  };
};

// 加载数据
const loadData = () => {
  loading.value = true;

  const params: any = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
  };

  // 如果有用户过滤，设置用户名参数
  if (props.userFilter) {
    params.username = props.userFilter;
    console.log('设置用户过滤:', props.userFilter);
  }

  // 设置搜索参数
  if (searchParams.search1) {
    params.cropType = searchParams.search1;
  }
  if (searchParams.search2) {
    params.keyword = searchParams.search2;
  }

  console.log('图像记录API请求参数:', params);

  request.get('/api/records/images', { params })
    .then((res) => {
      if (res.code === "0" || res.code === 0) {
        records.value = [];
        setTimeout(() => {
          loading.value = false;
        }, 500);

        for (let i = 0; i < res.data.records.length; i++) {
          const record = res.data.records[i];
          let transformedData;

          try {
            const confidences = JSON.parse(record.confidence);
            const labels = JSON.parse(record.label);
            transformedData = transformData(record, confidences, labels);
          } catch (e) {
            // 如果JSON解析失败，使用原始数据
            console.warn('JSON解析失败，使用原始数据:', e);
            transformedData = {
              ...record,
              results: [{
                label: record.label || '未知',
                confidence: record.conf ? (parseFloat(record.conf) * 100).toFixed(2) : '0'
              }]
            };
          }

          transformedData.num = (currentPage.value - 1) * pageSize.value + i + 1;
          records.value[i] = transformedData;
        }
        total.value = res.data.total;

        console.log('图像记录加载完成，记录数:', records.value.length);
      } else {
        loading.value = false;
        ElMessage.error(res.msg || '查询失败');
      }
    })
    .catch((error) => {
      loading.value = false;
      console.error('加载图像记录失败:', error);
      ElMessage.error('加载数据失败');
    });
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = '';
  });
  currentPage.value = 1;
  loadData();
};

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
  loadData();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  loadData();
};

// 删除记录
const deleteRecord = (record: any) => {
  ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    request.delete(`/api/records/images/${record.id}`)
      .then((res) => {
        if (res.code === "0" || res.code === 0) {
          ElMessage.success('删除成功');
          loadData();
        } else {
          ElMessage.error(res.msg || '删除失败');
        }
      })
      .catch((error) => {
        console.error('删除记录失败:', error);
        ElMessage.error('删除失败');
      });
  });
};

// 图片预览
const previewImage = (url: string) => {
  previewUrl.value = url;
  previewVisible.value = true;
};

// 获取置信度样式类
const getConfidenceClass = (confidence: number) => {
  if (confidence >= 80) return 'high';
  if (confidence >= 60) return 'medium';
  return 'low';
};

// 监听用户过滤变化
watch(() => props.userFilter, () => {
  loadData();
});

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});

// 暴露刷新方法给父组件
defineExpose({
  refresh: loadData
});
</script>

<style scoped lang="scss">
.image-records {
  .search-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
  }

  .records-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    .record-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }

      .record-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;

        .record-number {
          display: flex;
          align-items: center;
          font-weight: 600;

          .number-label {
            color: #6c757d;
            margin-right: 4px;
          }

          .number-value {
            color: #495057;
          }
        }
      }

      .record-content {
        padding: 16px;

        .image-section {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;

          .image-container {
            text-align: center;

            img {
              width: 120px;
              height: 120px;
              object-fit: cover;
              border-radius: 8px;
              cursor: pointer;
              transition: transform 0.2s ease;

              &:hover {
                transform: scale(1.05);
              }
            }

            .image-label {
              margin-top: 8px;
              font-size: 12px;
              color: #6c757d;
            }
          }

          .arrow-icon {
            color: #6c757d;
            font-size: 20px;
          }
        }

        .detection-info {
          margin-bottom: 16px;

          .info-row {
            display: flex;
            margin-bottom: 8px;

            .label {
              width: 80px;
              color: #6c757d;
              font-size: 14px;
            }

            .value {
              flex: 1;
              color: #495057;
              font-size: 14px;

              &.crop-type {
                font-weight: 600;
                color: #007bff;
              }
            }
          }
        }

        .detection-results {
          .results-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-weight: 600;
            color: #495057;

            .el-icon {
              margin-right: 8px;
            }
          }

          .results-list {
            .result-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 12px;
              margin-bottom: 8px;
              background: #f8f9fa;
              border-radius: 6px;

              .result-label {
                flex: 1;
                font-size: 14px;
                color: #495057;
              }

              .result-confidence {
                font-weight: 600;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 12px;

                &.high {
                  background: #d4edda;
                  color: #155724;
                }

                &.medium {
                  background: #fff3cd;
                  color: #856404;
                }

                &.low {
                  background: #f8d7da;
                  color: #721c24;
                }
              }
            }
          }
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .preview-container {
    text-align: center;

    .preview-image {
      max-width: 100%;
      max-height: 70vh;
      object-fit: contain;
    }
  }
}
</style>
