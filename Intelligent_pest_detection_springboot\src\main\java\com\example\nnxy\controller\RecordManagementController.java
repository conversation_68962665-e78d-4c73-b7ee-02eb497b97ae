package com.example.nnxy.controller;

import com.example.nnxy.dto.RecordQueryRequest;
import com.example.nnxy.dto.RecordOperationResult;
import com.example.nnxy.entity.ImgRecords;
import com.example.nnxy.entity.VideoRecords;
import com.example.nnxy.entity.CameraRecords;
import com.example.nnxy.mapper.ImgRecordsMapper;
import com.example.nnxy.mapper.VideoRecordsMapper;
import com.example.nnxy.mapper.CameraRecordsMapper;
import com.example.nnxy.common.Result;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 记录管理控制器 - 专门为AI聊天功能提供的记录查询API
 */
@Slf4j
@RestController
@RequestMapping("/records")
@CrossOrigin(origins = "*")
public class RecordManagementController {

    @Resource
    private ImgRecordsMapper imgRecordsMapper;

    @Resource
    private VideoRecordsMapper videoRecordsMapper;

    @Resource
    private CameraRecordsMapper cameraRecordsMapper;

    /**
     * 查询图像检测记录
     */
    @GetMapping("/images")
    public Result<?> getImageRecords(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String cropType,
            @RequestParam(required = false) String keyword) {
        
        log.info("查询图像记录 - 页码: {}, 页大小: {}, 用户: {}, 作物类型: {}, 关键词: {}", 
                pageNum, pageSize, username, cropType, keyword);

        try {
            LambdaQueryWrapper<ImgRecords> wrapper = Wrappers.<ImgRecords>lambdaQuery();
            wrapper.orderByDesc(ImgRecords::getStartTime);

            // 用户过滤
            if (StrUtil.isNotBlank(username)) {
                wrapper.eq(ImgRecords::getUsername, username);
            }

            // 作物类型过滤
            if (StrUtil.isNotBlank(cropType)) {
                wrapper.eq(ImgRecords::getKind, cropType);
            }

            // 关键词搜索（在标签中搜索）
            if (StrUtil.isNotBlank(keyword)) {
                wrapper.like(ImgRecords::getLabel, keyword);
            }

            // 分页查询
            Page<ImgRecords> page = new Page<>(pageNum, pageSize);
            Page<ImgRecords> result = imgRecordsMapper.selectPage(page, wrapper);

            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("records", result.getRecords());
            data.put("total", result.getTotal());
            data.put("current", result.getCurrent());
            data.put("pages", result.getPages());
            data.put("size", result.getSize());

            log.info("图像记录查询完成 - 总数: {}, 当前页: {}", result.getTotal(), result.getCurrent());

            return Result.success(data);
        } catch (Exception e) {
            log.error("查询图像记录失败", e);
            return Result.error("-1", "查询图像记录失败: " + e.getMessage());
        }
    }

    /**
     * 查询视频检测记录
     */
    @GetMapping("/videos")
    public Result<?> getVideoRecords(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String cropType) {
        
        log.info("查询视频记录 - 页码: {}, 页大小: {}, 用户: {}, 作物类型: {}", 
                pageNum, pageSize, username, cropType);

        try {
            LambdaQueryWrapper<VideoRecords> wrapper = Wrappers.<VideoRecords>lambdaQuery();
            wrapper.orderByDesc(VideoRecords::getStartTime);

            // 用户过滤
            if (StrUtil.isNotBlank(username)) {
                wrapper.eq(VideoRecords::getUsername, username);
            }

            // 作物类型过滤
            if (StrUtil.isNotBlank(cropType)) {
                wrapper.eq(VideoRecords::getKind, cropType);
            }

            // 分页查询
            Page<VideoRecords> page = new Page<>(pageNum, pageSize);
            Page<VideoRecords> result = videoRecordsMapper.selectPage(page, wrapper);

            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("records", result.getRecords());
            data.put("total", result.getTotal());
            data.put("current", result.getCurrent());
            data.put("pages", result.getPages());
            data.put("size", result.getSize());

            log.info("视频记录查询完成 - 总数: {}, 当前页: {}", result.getTotal(), result.getCurrent());

            return Result.success(data);
        } catch (Exception e) {
            log.error("查询视频记录失败", e);
            return Result.error("-1", "查询视频记录失败: " + e.getMessage());
        }
    }

    /**
     * 查询摄像头检测记录
     */
    @GetMapping("/cameras")
    public Result<?> getCameraRecords(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String cropType) {
        
        log.info("查询摄像头记录 - 页码: {}, 页大小: {}, 用户: {}, 作物类型: {}", 
                pageNum, pageSize, username, cropType);

        try {
            LambdaQueryWrapper<CameraRecords> wrapper = Wrappers.<CameraRecords>lambdaQuery();
            wrapper.orderByDesc(CameraRecords::getStartTime);

            // 用户过滤
            if (StrUtil.isNotBlank(username)) {
                wrapper.eq(CameraRecords::getUsername, username);
            }

            // 作物类型过滤
            if (StrUtil.isNotBlank(cropType)) {
                wrapper.eq(CameraRecords::getKind, cropType);
            }

            // 分页查询
            Page<CameraRecords> page = new Page<>(pageNum, pageSize);
            Page<CameraRecords> result = cameraRecordsMapper.selectPage(page, wrapper);

            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("records", result.getRecords());
            data.put("total", result.getTotal());
            data.put("current", result.getCurrent());
            data.put("pages", result.getPages());
            data.put("size", result.getSize());

            log.info("摄像头记录查询完成 - 总数: {}, 当前页: {}", result.getTotal(), result.getCurrent());

            return Result.success(data);
        } catch (Exception e) {
            log.error("查询摄像头记录失败", e);
            return Result.error("-1", "查询摄像头记录失败: " + e.getMessage());
        }
    }

    /**
     * 删除图像记录
     */
    @DeleteMapping("/images/{id}")
    public Result<?> deleteImageRecord(@PathVariable Integer id) {
        log.info("删除图像记录 - ID: {}", id);
        try {
            int result = imgRecordsMapper.deleteById(id);
            if (result > 0) {
                return Result.success("删除成功");
            } else {
                return Result.error("-1", "删除失败，记录不存在");
            }
        } catch (Exception e) {
            log.error("删除图像记录失败", e);
            return Result.error("-1", "删除失败: " + e.getMessage());
        }
    }

    /**
     * 删除视频记录
     */
    @DeleteMapping("/videos/{id}")
    public Result<?> deleteVideoRecord(@PathVariable Integer id) {
        log.info("删除视频记录 - ID: {}", id);
        try {
            int result = videoRecordsMapper.deleteById(id);
            if (result > 0) {
                return Result.success("删除成功");
            } else {
                return Result.error("-1", "删除失败，记录不存在");
            }
        } catch (Exception e) {
            log.error("删除视频记录失败", e);
            return Result.error("-1", "删除失败: " + e.getMessage());
        }
    }

    /**
     * 删除摄像头记录
     */
    @DeleteMapping("/cameras/{id}")
    public Result<?> deleteCameraRecord(@PathVariable Integer id) {
        log.info("删除摄像头记录 - ID: {}", id);
        try {
            int result = cameraRecordsMapper.deleteById(id);
            if (result > 0) {
                return Result.success("删除成功");
            } else {
                return Result.error("-1", "删除失败，记录不存在");
            }
        } catch (Exception e) {
            log.error("删除摄像头记录失败", e);
            return Result.error("-1", "删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取记录统计信息
     */
    @GetMapping("/statistics")
    public Result<?> getStatistics(@RequestParam(required = false) String username) {
        log.info("获取记录统计信息 - 用户: {}", username);

        try {
            Map<String, Object> statistics = new HashMap<>();

            // 构建查询条件
            LambdaQueryWrapper<ImgRecords> imgWrapper = Wrappers.<ImgRecords>lambdaQuery();
            LambdaQueryWrapper<VideoRecords> videoWrapper = Wrappers.<VideoRecords>lambdaQuery();
            LambdaQueryWrapper<CameraRecords> cameraWrapper = Wrappers.<CameraRecords>lambdaQuery();

            if (StrUtil.isNotBlank(username)) {
                imgWrapper.eq(ImgRecords::getUsername, username);
                videoWrapper.eq(VideoRecords::getUsername, username);
                cameraWrapper.eq(CameraRecords::getUsername, username);
            }

            // 统计各类型记录数量
            long imageCount = imgRecordsMapper.selectCount(imgWrapper);
            long videoCount = videoRecordsMapper.selectCount(videoWrapper);
            long cameraCount = cameraRecordsMapper.selectCount(cameraWrapper);
            long totalCount = imageCount + videoCount + cameraCount;

            statistics.put("imageCount", imageCount);
            statistics.put("videoCount", videoCount);
            statistics.put("cameraCount", cameraCount);
            statistics.put("totalCount", totalCount);

            log.info("统计信息获取完成 - 图像: {}, 视频: {}, 摄像头: {}, 总计: {}", 
                    imageCount, videoCount, cameraCount, totalCount);

            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return Result.error("-1", "获取统计信息失败: " + e.getMessage());
        }
    }


}
