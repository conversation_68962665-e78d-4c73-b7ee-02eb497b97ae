<template>
	<div class="pest-page-container">
		<div class="pest-content-wrapper">
			<!-- 页面标题 -->
			<div class="page-header animate-fade-in-up">
				<h1 class="pest-title">图像害虫检测</h1>
				<p class="pest-subtitle">上传图片，使用YOLO11算法智能识别农作物害虫</p>
			</div>

			<!-- 控制面板 -->
			<div class="control-panel pest-card animate-fade-in-up animate-delay-100">
				<div class="control-header">
					<i class="iconfont icon-shezhi"></i>
					<span>检测参数设置</span>
				</div>

				<div class="control-content">
					<div class="control-row">
						<div class="control-item">
							<label class="control-label">作物种类</label>
							<el-select
								v-model="kind"
								placeholder="请选择作物种类"
								size="large"
								class="control-select"
								@change="getData"
							>
								<el-option
									v-for="item in state.kind_items"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>

						<div class="control-item">
							<label class="control-label">检测模型</label>
							<el-select
								v-model="weight"
								placeholder="请选择模型"
								size="large"
								class="control-select"
							>
								<el-option
									v-for="item in state.weight_items"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>

						<div class="control-item confidence-item">
							<label class="control-label">置信度阈值</label>
							<div class="confidence-slider">
								<el-slider
									v-model="conf"
									:format-tooltip="formatTooltip"
									:show-tooltip="true"
									class="slider"
								/>
								<span class="confidence-value">{{ formatTooltip(conf) }}</span>
							</div>
						</div>

						<div class="control-item">
							<el-button
								type="primary"
								@click="upData"
								class="pest-btn-primary detect-button"
								size="large"
								:disabled="!imageUrl || !weight || !kind"
							>
								<i class="iconfont icon-shibie" style="margin-right: 8px;"></i>
								开始检测
							</el-button>
						</div>
					</div>
				</div>
			</div>

			<!-- 主要内容区域 -->
			<div class="main-content">
				<!-- 图片上传区域 -->
				<div class="upload-section pest-card animate-fade-in-left animate-delay-200">
					<div class="upload-header">
						<i class="iconfont icon-tupian"></i>
						<span>图片上传</span>
					</div>

					<div class="upload-area">
						<el-upload
							v-model="state.img"
							ref="uploadFile"
							class="image-uploader"
							action="http://localhost:9999/files/upload"
							:show-file-list="false"
							:on-success="handleAvatarSuccessone"
							accept="image/*"
						>
							<div v-if="imageUrl" class="image-preview">
								<img :src="imageUrl" class="uploaded-image" />
								<div class="image-overlay">
									<i class="iconfont icon-bianji"></i>
									<span>点击更换图片</span>
								</div>
							</div>
							<div v-else class="upload-placeholder">
								<i class="iconfont icon-shangchuan"></i>
								<p class="upload-text">点击上传图片</p>
								<p class="upload-hint">支持 JPG、PNG、JPEG 格式</p>
							</div>
						</el-upload>
					</div>
				</div>

				<!-- 检测结果区域 -->
				<div class="result-section pest-card-result animate-fade-in-right animate-delay-300" v-if="state.predictionResult.label">
					<div class="result-header">
						<i class="iconfont icon-jieguo"></i>
						<span>检测结果</span>
					</div>

					<div class="result-content">
						<div class="result-stats">
							<div class="stat-item">
								<div class="stat-value">{{ state.predictionResult.label }}</div>
								<div class="stat-label">识别结果</div>
							</div>
							<div class="stat-item">
								<div class="stat-value">{{ state.predictionResult.confidence }}</div>
								<div class="stat-label">置信度</div>
							</div>
							<div class="stat-item">
								<div class="stat-value">{{ state.predictionResult.allTime }}</div>
								<div class="stat-label">处理时间</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 功能说明 -->
			<div class="feature-info pest-card animate-fade-in-up animate-delay-400">
				<div class="info-header">
					<i class="iconfont icon-bangzhu"></i>
					<span>使用说明</span>
				</div>
				<div class="info-content">
					<div class="info-steps">
						<div class="step-item">
							<div class="step-number">1</div>
							<div class="step-content">
								<h4>选择作物种类</h4>
								<p>根据您要检测的作物选择对应的种类</p>
							</div>
						</div>
						<div class="step-item">
							<div class="step-number">2</div>
							<div class="step-content">
								<h4>选择检测模型</h4>
								<p>系统会根据作物种类自动筛选适合的模型</p>
							</div>
						</div>
						<div class="step-item">
							<div class="step-number">3</div>
							<div class="step-content">
								<h4>调整置信度</h4>
								<p>设置检测的敏感度，数值越高越严格</p>
							</div>
						</div>
						<div class="step-item">
							<div class="step-number">4</div>
							<div class="step-content">
								<h4>上传并检测</h4>
								<p>上传清晰的作物图片，点击开始检测</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>


<script setup lang="ts" name="personal">
import { reactive, ref, onMounted } from 'vue';
import type { UploadInstance, UploadProps } from 'element-plus';
import { ElMessage } from 'element-plus';
import request from '/@/utils/request';
import { Plus } from '@element-plus/icons-vue';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
import { formatDate } from '/@/utils/formatTime';

const imageUrl = ref('');
const conf = ref('');
const weight = ref('');
const kind = ref('');
const uploadFile = ref<UploadInstance>();
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);
const state = reactive({
	weight_items: [] as any,
	kind_items: [
		{
			value: 'corn',
			label: '玉米',
		},
		{
			value: 'rice',
			label: '水稻',
		},
		{
			value: 'strawberry',
			label: '草莓',
		},
		{
			value: 'tomato',
			label: '西红柿',
		},
	],
	img: '',
	predictionResult: {
		label: '',
		confidence: '',
		allTime: '',
	},
	form: {
		username: '',
		inputImg: null as any,
		weight: '',
		conf: null as any,
		kind: '',
		startTime: ''
	},
});

const formatTooltip = (val: number) => {
	return val / 100
}

const handleAvatarSuccessone: UploadProps['onSuccess'] = (response, uploadFile) => {
	imageUrl.value = URL.createObjectURL(uploadFile.raw!);
	state.img = response.data;
};

const getData = () => {
	request.get('/api/flask/file_names').then((res) => {
		if (res.code == 0) {
			res.data = JSON.parse(res.data);
			state.weight_items = res.data.weight_items.filter(item => item.value.includes(kind.value));
		} else {
			ElMessage.error(res.msg);
		}
	});
};


const upData = () => {
	state.form.weight = weight.value;
	state.form.conf = (parseFloat(conf.value) / 100);
	state.form.username = userInfos.value.userName;
	state.form.inputImg = state.img;
	state.form.kind = kind.value;
	state.form.startTime = formatDate(new Date(), 'YYYY-mm-dd HH:MM:SS');
	console.log(state.form);
	request.post('/api/flask/predict', state.form).then((res) => {
		if (res.code == 0) {
			try {
				res.data = JSON.parse(res.data);

				// 如果 res.data.label 是字符串，则解析为数组
				if (typeof res.data.label === 'string') {
					res.data.label = JSON.parse(res.data.label);
				}

				// 确保 res.data.label 是数组后再调用 map
				if (Array.isArray(res.data.label)) {
					state.predictionResult.label = res.data.label.map(item => item.replace(/\\u([\dA-Fa-f]{4})/g, (_, code) =>
						String.fromCharCode(parseInt(code, 16))
					));
				} else {
					console.error("res.data.label 不是数组:", res.data.label);
				}
				state.predictionResult.confidence = res.data.confidence;
				state.predictionResult.allTime = res.data.allTime;

				// 覆盖原图片
				if (res.data.outImg) {
					// 使用服务器返回的新图片路径
					imageUrl.value = res.data.outImg;
				} else {
					// 否则保留原图片路径
					imageUrl.value = imageUrl.value;
				}
				console.log(state.predictionResult);
			} catch (error) {
				console.error('解析 JSON 时出错:', error);
			}
			ElMessage.success('预测成功！');
		} else {
			ElMessage.error(res.msg);
		}
	});
};

onMounted(() => {
	getData();
});
</script>

<style scoped lang="scss">
.page-header {
	text-align: center;
	margin-bottom: var(--spacing-xl);
}

.control-panel {
	margin-bottom: var(--spacing-xl);

	.control-header {
		display: flex;
		align-items: center;
		gap: var(--spacing-sm);
		margin-bottom: var(--spacing-lg);
		font-size: 1.125rem;
		font-weight: 600;
		color: var(--text-primary);

		i {
			color: var(--primary-color);
			font-size: 1.25rem;
		}
	}

	.control-content {
		.control-row {
			display: grid;
			grid-template-columns: 1fr 1fr 2fr auto;
			gap: var(--spacing-lg);
			align-items: end;
		}

		.control-item {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-sm);

			.control-label {
				font-size: 0.875rem;
				font-weight: 500;
				color: var(--text-secondary);
			}

			.control-select {
				width: 100%;
			}
		}

		.confidence-item {
			.confidence-slider {
				display: flex;
				align-items: center;
				gap: var(--spacing-md);

				.slider {
					flex: 1;
				}

				.confidence-value {
					min-width: 40px;
					text-align: center;
					font-weight: 600;
					color: var(--primary-color);
				}
			}
		}

		.detect-button {
			height: 48px;
			padding: 0 var(--spacing-xl);
			font-weight: 600;
		}
	}
}

.main-content {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: var(--spacing-xl);
	margin-bottom: var(--spacing-xl);
}

.upload-section, .result-section {
	.upload-header, .result-header {
		display: flex;
		align-items: center;
		gap: var(--spacing-sm);
		margin-bottom: var(--spacing-lg);
		font-size: 1.125rem;
		font-weight: 600;
		color: var(--text-primary);

		i {
			color: var(--primary-color);
			font-size: 1.25rem;
		}
	}
}

.upload-area {
	.image-uploader {
		width: 100%;

		:deep(.el-upload) {
			width: 100%;
			border: none;
		}
	}

	.image-preview {
		position: relative;
		width: 100%;
		height: 400px;
		border-radius: var(--radius-lg);
		overflow: hidden;
		cursor: pointer;

		.uploaded-image {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.image-overlay {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.5);
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			color: white;
			opacity: 0;
			transition: opacity var(--transition-normal);

			i {
				font-size: 2rem;
				margin-bottom: var(--spacing-sm);
			}
		}

		&:hover .image-overlay {
			opacity: 1;
		}
	}

	.upload-placeholder {
		width: 100%;
		height: 400px;
		border: 2px dashed var(--border-medium);
		border-radius: var(--radius-lg);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all var(--transition-normal);
		background: var(--bg-secondary);

		&:hover {
			border-color: var(--primary-light);
			background: var(--bg-hover);
		}

		i {
			font-size: 3rem;
			color: var(--text-muted);
			margin-bottom: var(--spacing-md);
		}

		.upload-text {
			font-size: 1.125rem;
			font-weight: 500;
			color: var(--text-secondary);
			margin-bottom: var(--spacing-sm);
		}

		.upload-hint {
			font-size: 0.875rem;
			color: var(--text-muted);
		}
	}
}

.result-content {
	.result-stats {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: var(--spacing-lg);

		.stat-item {
			text-align: center;
			padding: var(--spacing-lg);
			background: var(--bg-secondary);
			border-radius: var(--radius-md);

			.stat-value {
				font-size: 1.5rem;
				font-weight: 700;
				color: var(--primary-color);
				margin-bottom: var(--spacing-sm);
			}

			.stat-label {
				font-size: 0.875rem;
				color: var(--text-secondary);
				font-weight: 500;
			}
		}
	}
}

.feature-info {
	.info-header {
		display: flex;
		align-items: center;
		gap: var(--spacing-sm);
		margin-bottom: var(--spacing-lg);
		font-size: 1.125rem;
		font-weight: 600;
		color: var(--text-primary);

		i {
			color: var(--secondary-color);
			font-size: 1.25rem;
		}
	}

	.info-steps {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: var(--spacing-lg);

		.step-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			text-align: center;

			.step-number {
				width: 40px;
				height: 40px;
				background: var(--secondary-gradient);
				color: white;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: 700;
				margin-bottom: var(--spacing-md);
			}

			.step-content {
				h4 {
					font-size: 1rem;
					font-weight: 600;
					color: var(--text-primary);
					margin-bottom: var(--spacing-sm);
				}

				p {
					font-size: 0.875rem;
					color: var(--text-secondary);
					line-height: 1.5;
				}
			}
		}
	}
}

// 响应式设计
@media (max-width: 1024px) {
	.control-content .control-row {
		grid-template-columns: 1fr 1fr;
		gap: var(--spacing-md);

		.confidence-item {
			grid-column: 1 / -1;
		}

		.detect-button {
			grid-column: 1 / -1;
			justify-self: center;
		}
	}

	.main-content {
		grid-template-columns: 1fr;
	}

	.info-steps {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 768px) {
	.control-content .control-row {
		grid-template-columns: 1fr;
	}

	.info-steps {
		grid-template-columns: 1fr;
	}

	.result-stats {
		grid-template-columns: 1fr;
	}
}
</style>
