package com.example.nnxy.service;

import com.example.nnxy.dto.AnalysisReportDTO;
import com.example.nnxy.dto.ChatRequest;
import com.example.nnxy.dto.ChatResponse;
import com.example.nnxy.dto.RecordOperationResult;
import com.example.nnxy.dto.RecordQueryRequest;
import com.example.nnxy.entity.ChatMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import jakarta.annotation.PostConstruct;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class ChatService {

    @Value("${deepseek.api.key}")
    private String apiKey;

    @Value("${deepseek.api.url}")
    private String apiUrl;

    @Value("${deepseek.model}")
    private String model;

    @Value("${deepseek.temperature}")
    private Double defaultTemperature;

    @Autowired
    private CommandParserService commandParserService;

    @Autowired
    private RecordManagementService recordManagementService;

    @Autowired
    private ReportAnalysisService reportAnalysisService;

    private final RestTemplate restTemplate;
    private final Map<String, List<ChatMessage>> sessionHistory = new ConcurrentHashMap<>();
    private final Map<String, RecordQueryRequest> pendingDeleteOperations = new ConcurrentHashMap<>();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    private static final String SYSTEM_PROMPT =
        "你是一个专业的农业害虫检测AI助手，名字叫做'小虫博士'。你的主要职责是：\n" +
        "1. 帮助用户了解和使用害虫检测系统的各项功能\n" +
        "2. 回答关于农作物病虫害的专业问题\n" +
        "3. 提供农业种植和病虫害防治的建议\n" +
        "4. 解释检测结果和推荐防治方案\n" +
        "5. 协助用户操作系统界面和功能\n" +
        "6. 智能管理检测记录，包括查询、搜索、删除等操作\n\n" +
        "当用户询问关于检测记录的查询、删除、统计等操作时，你可以直接执行这些操作并返回结果。\n" +
        "支持的记录管理指令包括：\n" +
        "- 查询/搜索记录：'查看我的图像检测记录'、'搜索玉米相关的记录'、'显示今天的检测记录'\n" +
        "- 删除记录：'删除昨天的记录'、'清除置信度低于50%的记录'\n" +
        "- 统计记录：'统计本月的检测数量'、'计算草莓检测记录有多少条'\n\n" +
        "请用专业、友好、简洁的语言回答用户问题。如果遇到不确定的问题，请诚实说明并建议咨询专业农技人员。";
    
    public ChatService() {
        this.restTemplate = new RestTemplate();
    }
    
    @PostConstruct
    public void init() {
        log.info("ChatService初始化完成");
        log.info("API URL: {}", apiUrl);
        log.info("Model: {}", model);
        if (apiKey != null && apiKey.length() > 10) {
            log.info("API Key: {}***", apiKey.substring(0, 10));
        } else {
            log.warn("API Key未正确配置");
        }
    }
    
    public ChatResponse sendMessage(ChatRequest request) {
        log.info("开始处理聊天请求: {}", request.getMessage());

        try {
            String sessionId = request.getSessionId();
            if (sessionId == null || sessionId.isEmpty()) {
                sessionId = UUID.randomUUID().toString();
            }

            ChatMessage userMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .content(request.getMessage())
                .role("user")
                .username(request.getUsername())
                .timestamp(LocalDateTime.now())
                .status("sent")
                .sessionId(sessionId)
                .build();

            List<ChatMessage> history = sessionHistory.computeIfAbsent(sessionId, k -> new ArrayList<>());
            history.add(userMessage);

            // 检查是否有待确认的删除操作
            if (pendingDeleteOperations.containsKey(sessionId)) {
                log.info("检测到待确认的删除操作，处理确认指令");
                return handleDeleteConfirmation(request, userMessage, sessionId, history);
            }

            // 检查是否为报告生成指令
            if (commandParserService.isReportGenerationCommand(request.getMessage())) {
                log.info("检测到报告生成指令，执行报告生成操作");
                return handleReportGenerationCommand(request, userMessage, sessionId, history);
            }

            // 检查是否为记录管理指令
            if (commandParserService.isRecordManagementCommand(request.getMessage())) {
                log.info("检测到记录管理指令，执行自动化操作");
                return handleRecordManagementCommand(request, userMessage, sessionId, history);
            }
            
            List<Map<String, String>> messages = new ArrayList<>();
            
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", SYSTEM_PROMPT);
            messages.add(systemMessage);
            
            int startIndex = Math.max(0, history.size() - 10);
            for (int i = startIndex; i < history.size(); i++) {
                ChatMessage msg = history.get(i);
                Map<String, String> message = new HashMap<>();
                message.put("role", msg.getRole());
                message.put("content", msg.getContent());
                messages.add(message);
            }
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            requestBody.put("messages", messages);
            requestBody.put("temperature", request.getTemperature() != null ? request.getTemperature() : defaultTemperature);
            requestBody.put("stream", false);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            log.info("调用DeepSeek API: {}", apiUrl);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(apiUrl, entity, Map.class);
            log.info("API响应状态: {}", response.getStatusCode());
            
            Map<String, Object> responseBody = response.getBody();
            log.info("API响应体: {}", responseBody);
            
            if (responseBody != null) {
                log.info("响应体包含的键: {}", responseBody.keySet());
                
                if (responseBody.containsKey("choices")) {
                    List<Map<String, Object>> choices = (List<Map<String, Object>>) responseBody.get("choices");
                    log.info("choices数量: {}", choices.size());
                    
                    if (!choices.isEmpty()) {
                        Map<String, Object> choice = choices.get(0);
                        log.info("第一个choice: {}", choice);
                        
                        Map<String, Object> message = (Map<String, Object>) choice.get("message");
                        log.info("message内容: {}", message);
                        
                        String aiContent = (String) message.get("content");
                        log.info("AI回复内容: {}", aiContent);
                        
                        ChatMessage aiMessage = ChatMessage.builder()
                            .id(UUID.randomUUID().toString())
                            .content(aiContent)
                            .role("assistant")
                            .username("小虫博士")
                            .timestamp(LocalDateTime.now())
                            .status("sent")
                            .sessionId(sessionId)
                            .build();
                        
                        history.add(aiMessage);
                        
                        if (history.size() > 20) {
                            history.subList(0, history.size() - 20).clear();
                        }
                        
                        log.info("成功创建AI消息，返回响应");
                        return ChatResponse.builder()
                            .userMessage(userMessage)
                            .aiMessage(aiMessage)
                            .sessionId(sessionId)
                            .success(true)
                            .build();
                    } else {
                        log.error("choices列表为空");
                    }
                } else {
                    log.error("响应中没有choices字段");
                }
            } else {
                log.error("响应体为空");
            }
            
            log.error("AI API响应格式错误，完整响应: {}", responseBody);
            return ChatResponse.builder()
                .success(false)
                .error("AI API响应格式错误")
                .build();
                
        } catch (Exception e) {
            log.error("聊天服务异常", e);
            return ChatResponse.builder()
                .success(false)
                .error("AI服务暂时不可用，请稍后重试: " + e.getMessage())
                .build();
        }
    }
    
    public List<ChatMessage> getSessionHistory(String sessionId) {
        return sessionHistory.getOrDefault(sessionId, new ArrayList<>());
    }
    
    public void clearSessionHistory(String sessionId) {
        sessionHistory.remove(sessionId);
    }
    
    public Set<String> getAllSessionIds() {
        return sessionHistory.keySet();
    }

    public boolean testApiConnection() {
        try {
            ChatRequest testRequest = new ChatRequest();
            testRequest.setMessage("Hello");
            testRequest.setUsername("test");
            testRequest.setTemperature(0.1);

            ChatResponse response = sendMessage(testRequest);
            return response.getSuccess();
        } catch (Exception e) {
            log.error("API连接测试失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 流式发送消息
     */
    public void sendStreamMessage(ChatRequest request, SseEmitter emitter) {
        log.info("开始流式处理聊天请求: {}", request.getMessage());

        try {
            String sessionId = request.getSessionId();
            if (sessionId == null || sessionId.isEmpty()) {
                sessionId = UUID.randomUUID().toString();
            }

            // 创建用户消息
            ChatMessage userMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .content(request.getMessage())
                .role("user")
                .username(request.getUsername())
                .timestamp(LocalDateTime.now())
                .status("sent")
                .sessionId(sessionId)
                .build();

            // 发送用户消息
            emitter.send(SseEmitter.event()
                .name("userMessage")
                .data(userMessage));

            // 获取会话历史
            List<ChatMessage> history = sessionHistory.computeIfAbsent(sessionId, k -> new ArrayList<>());
            history.add(userMessage);

            // 构建请求消息列表
            List<Map<String, String>> messages = new ArrayList<>();

            // 添加系统消息
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", SYSTEM_PROMPT);
            messages.add(systemMessage);

            // 添加历史消息（最近10条）
            int startIndex = Math.max(0, history.size() - 10);
            for (int i = startIndex; i < history.size(); i++) {
                ChatMessage msg = history.get(i);
                Map<String, String> message = new HashMap<>();
                message.put("role", msg.getRole());
                message.put("content", msg.getContent());
                messages.add(message);
            }

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            requestBody.put("messages", messages);
            requestBody.put("temperature", request.getTemperature() != null ? request.getTemperature() : defaultTemperature);
            requestBody.put("stream", true);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            log.info("调用DeepSeek流式API: {}", apiUrl);

            // 模拟流式响应（因为实际的流式API调用比较复杂，这里先用模拟的方式）
            simulateStreamResponse(request, sessionId, emitter);

        } catch (Exception e) {
            log.error("流式聊天服务异常", e);
            try {
                emitter.send(SseEmitter.event()
                    .name("error")
                    .data("AI服务暂时不可用，请稍后重试: " + e.getMessage()));
                emitter.complete();
            } catch (Exception ex) {
                emitter.completeWithError(ex);
            }
        }
    }

    /**
     * 模拟流式响应
     */
    private void simulateStreamResponse(ChatRequest request, String sessionId, SseEmitter emitter) throws Exception {
        // 先调用普通API获取完整回复
        ChatResponse fullResponse = sendMessage(request);

        if (fullResponse.getSuccess()) {
            String fullContent = fullResponse.getAiMessage().getContent();

            // 创建AI消息基础信息
            ChatMessage aiMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .content("")
                .role("assistant")
                .username("小虫博士")
                .timestamp(LocalDateTime.now())
                .status("sending")
                .sessionId(sessionId)
                .build();

            // 发送开始事件
            emitter.send(SseEmitter.event()
                .name("start")
                .data(aiMessage));

            // 逐字发送内容
            StringBuilder currentContent = new StringBuilder();
            for (int i = 0; i < fullContent.length(); i++) {
                char c = fullContent.charAt(i);
                currentContent.append(c);

                // 发送增量内容
                Map<String, Object> delta = new HashMap<>();
                delta.put("id", aiMessage.getId());
                delta.put("content", currentContent.toString());
                delta.put("delta", String.valueOf(c));

                emitter.send(SseEmitter.event()
                    .name("delta")
                    .data(delta));

                // 控制发送速度
                Thread.sleep(50); // 50ms间隔
            }

            // 发送完成事件
            aiMessage.setContent(fullContent);
            aiMessage.setStatus("sent");

            emitter.send(SseEmitter.event()
                .name("complete")
                .data(aiMessage));

            // 添加到历史记录
            List<ChatMessage> history = sessionHistory.get(sessionId);
            if (history != null) {
                history.add(aiMessage);

                // 限制历史记录长度
                if (history.size() > 20) {
                    history.subList(0, history.size() - 20).clear();
                }
            }

            emitter.complete();
        } else {
            emitter.send(SseEmitter.event()
                .name("error")
                .data(fullResponse.getError()));
            emitter.complete();
        }
    }

    /**
     * 处理报告生成指令
     */
    private ChatResponse handleReportGenerationCommand(ChatRequest request, ChatMessage userMessage, String sessionId, List<ChatMessage> history) {
        try {
            log.info("开始处理报告生成指令: {}", request.getMessage());

            // 解析指令
            RecordQueryRequest queryRequest = commandParserService.parseCommand(request.getMessage(), request.getUsername());

            // 确保是报告生成操作
            queryRequest.setOperationType("REPORT");
            queryRequest.setGenerateReport(true);

            // 执行报告生成
            RecordOperationResult operationResult = recordManagementService.executeOperation(queryRequest);

            // 生成AI回复
            String aiContent = generateRecordManagementResponse(operationResult, queryRequest);

            // 如果生成了报告，在回复中添加查看报告按钮
            if ("REPORT".equals(operationResult.getOperationType()) && operationResult.getData() instanceof AnalysisReportDTO) {
                aiContent += "\n\n" + generateReportViewButton();
            }

            ChatMessage aiMessage = ChatMessage.builder()
                    .id(UUID.randomUUID().toString())
                    .content(aiContent)
                    .role("assistant")
                    .timestamp(LocalDateTime.now())
                    .status("sent")
                    .sessionId(sessionId)
                    .build();

            // 保存消息到历史记录
            history.add(userMessage);
            history.add(aiMessage);

            // 限制历史记录长度
            if (history.size() > 20) {
                history.subList(0, history.size() - 20).clear();
            }

            ChatResponse.ChatResponseBuilder responseBuilder = ChatResponse.builder()
                .userMessage(userMessage)
                .aiMessage(aiMessage)
                .sessionId(sessionId)
                .success(true)
                .recordOperationResult(operationResult);

            // 如果是报告生成操作，添加报告数据
            if ("REPORT".equals(operationResult.getOperationType()) && operationResult.getData() instanceof AnalysisReportDTO) {
                responseBuilder.reportData((AnalysisReportDTO) operationResult.getData());
            }

            return responseBuilder.build();

        } catch (Exception e) {
            log.error("处理报告生成指令失败", e);

            ChatMessage aiMessage = ChatMessage.builder()
                    .id(UUID.randomUUID().toString())
                    .content("抱歉，生成报告时出现错误：" + e.getMessage())
                    .role("assistant")
                    .timestamp(LocalDateTime.now())
                    .status("sent")
                    .sessionId(sessionId)
                    .build();

            return ChatResponse.builder()
                    .userMessage(userMessage)
                    .aiMessage(aiMessage)
                    .sessionId(sessionId)
                    .success(false)
                    .error(e.getMessage())
                    .build();
        }
    }

    /**
     * 生成查看报告按钮的HTML
     */
    private String generateReportViewButton() {
        return "<div class=\"report-action-buttons\">" +
               "<button class=\"btn-view-report\" onclick=\"handleViewReport()\">" +
               "📊 查看完整报告" +
               "</button>" +
               "<script>" +
               "function handleViewReport() {" +
               "  if (window.parent && window.parent !== window) {" +
               "    window.parent.postMessage({type: 'VIEW_REPORT'}, '*');" +
               "  } else if (window.top && window.top !== window) {" +
               "    window.top.postMessage({type: 'VIEW_REPORT'}, '*');" +
               "  } else {" +
               "    window.postMessage({type: 'VIEW_REPORT'}, '*');" +
               "  }" +
               "}" +
               "</script>" +
               "</div>";
    }

    /**
     * 处理记录管理指令
     */
    private ChatResponse handleRecordManagementCommand(ChatRequest request, ChatMessage userMessage,
                                                      String sessionId, List<ChatMessage> history) {
        try {
            // 解析用户指令
            RecordQueryRequest queryRequest = commandParserService.parseCommand(request.getMessage(), request.getUsername());
            log.info("解析的查询请求: {}", queryRequest);

            // 执行记录操作
            RecordOperationResult operationResult = recordManagementService.executeOperation(queryRequest);
            log.info("操作执行结果: {}", operationResult);

            // 如果需要确认且是删除操作，保存待确认的操作
            if (operationResult.getNeedConfirmation() != null && operationResult.getNeedConfirmation() && "DELETE".equals(queryRequest.getOperationType())) {
                pendingDeleteOperations.put(sessionId, queryRequest);
            }

            // 生成AI回复内容
            String aiContent = generateRecordManagementResponse(operationResult, queryRequest);

            // 创建AI消息
            ChatMessage aiMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .content(aiContent)
                .role("assistant")
                .username("小虫博士")
                .timestamp(LocalDateTime.now())
                .status("sent")
                .sessionId(sessionId)
                .recordOperationResult(operationResult) // 添加操作结果到消息中
                .build();

            history.add(aiMessage);

            // 限制历史记录长度
            if (history.size() > 20) {
                history.subList(0, history.size() - 20).clear();
            }

            ChatResponse.ChatResponseBuilder responseBuilder = ChatResponse.builder()
                .userMessage(userMessage)
                .aiMessage(aiMessage)
                .sessionId(sessionId)
                .success(true)
                .recordOperationResult(operationResult);

            // 如果是报告生成操作，添加报告数据
            if ("REPORT".equals(operationResult.getOperationType()) && operationResult.getData() instanceof AnalysisReportDTO) {
                responseBuilder.reportData((AnalysisReportDTO) operationResult.getData());
            }

            return responseBuilder.build();

        } catch (Exception e) {
            log.error("处理记录管理指令失败", e);

            String errorContent = "抱歉，处理您的记录管理请求时出现了问题：" + e.getMessage() +
                "\n\n您可以尝试使用以下格式的指令：\n" +
                "• 查询记录：'查看我的图像检测记录'、'搜索玉米相关的记录'\n" +
                "• 删除记录：'删除昨天的记录'、'清除置信度低于50%的记录'\n" +
                "• 统计记录：'统计本月的检测数量'";

            ChatMessage aiMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .content(errorContent)
                .role("assistant")
                .username("小虫博士")
                .timestamp(LocalDateTime.now())
                .status("sent")
                .sessionId(sessionId)
                .build();

            history.add(aiMessage);

            return ChatResponse.builder()
                .userMessage(userMessage)
                .aiMessage(aiMessage)
                .sessionId(sessionId)
                .success(true)
                .build();
        }
    }

    /**
     * 生成记录管理操作的回复内容
     */
    private String generateRecordManagementResponse(RecordOperationResult result, RecordQueryRequest request) {
        StringBuilder response = new StringBuilder();

        if (result.getSuccess()) {
            // 成功操作的回复
            response.append("✅ ").append(result.getMessage()).append("\n\n");

            // 根据操作类型添加详细信息
            switch (result.getOperationType()) {
                case "QUERY":
                    response.append(generateQueryResponse(result));
                    break;
                case "COUNT":
                    response.append(generateCountResponse(result));
                    break;
                case "DELETE":
                    response.append(generateDeleteResponse(result));
                    break;
                case "REPORT":
                    response.append(generateReportResponse(result));
                    break;
            }

            // 添加执行时间信息
            if (result.getExecutionTime() != null) {
                response.append("\n⏱️ 执行时间：").append(result.getExecutionTime()).append("ms");
            }

        } else if (result.getNeedConfirmation() != null && result.getNeedConfirmation()) {
            // 需要确认的操作
            response.append("⚠️ ").append(result.getConfirmationMessage()).append("\n\n");
            response.append("如果确认要执行此操作，请回复：'确认删除' 或 '是的，删除'");

        } else {
            // 操作失败
            response.append("❌ 操作失败：").append(result.getError()).append("\n\n");
            response.append("请检查您的指令格式，或尝试以下示例：\n");
            response.append("• 查询：'查看我今天的图像检测记录'\n");
            response.append("• 统计：'统计本周的检测数量'\n");
            response.append("• 删除：'删除昨天的记录'");
        }

        return response.toString();
    }

    /**
     * 生成查询操作的回复内容
     */
    private String generateQueryResponse(RecordOperationResult result) {
        StringBuilder response = new StringBuilder();

        if (result.getTotalCount() != null && result.getTotalCount() > 0) {
            response.append("📊 查询结果详情：\n");
            response.append("• 总记录数：").append(result.getTotalCount()).append(" 条\n");

            if (result.getCurrentPage() != null && result.getTotalPages() != null) {
                response.append("• 当前页：第 ").append(result.getCurrentPage())
                    .append(" 页，共 ").append(result.getTotalPages()).append(" 页\n");
            }

            // 如果有统计信息，显示详细统计
            if (result.getStatistics() != null) {
                Map<String, Object> stats = result.getStatistics();
                if (stats.containsKey("imageCount")) {
                    response.append("• 图像检测：").append(stats.get("imageCount")).append(" 条\n");
                }
                if (stats.containsKey("videoCount")) {
                    response.append("• 视频检测：").append(stats.get("videoCount")).append(" 条\n");
                }
                if (stats.containsKey("cameraCount")) {
                    response.append("• 摄像头检测：").append(stats.get("cameraCount")).append(" 条\n");
                }
            }

            response.append("\n💡 您可以在记录管理页面查看详细信息，或继续使用语音指令进行筛选。");
        } else {
            response.append("📭 未找到符合条件的记录。\n\n");
            response.append("💡 建议：\n");
            response.append("• 尝试扩大时间范围\n");
            response.append("• 检查作物类型是否正确\n");
            response.append("• 使用更宽泛的搜索条件");
        }

        return response.toString();
    }

    /**
     * 生成统计操作的回复内容
     */
    private String generateCountResponse(RecordOperationResult result) {
        StringBuilder response = new StringBuilder();

        response.append("📈 统计结果：\n");

        if (result.getStatistics() != null) {
            Map<String, Object> stats = result.getStatistics();

            if (stats.containsKey("totalCount")) {
                response.append("• 总计：").append(stats.get("totalCount")).append(" 条记录\n");
            }

            if (stats.containsKey("imageCount")) {
                response.append("• 图像检测：").append(stats.get("imageCount")).append(" 条\n");
            }
            if (stats.containsKey("videoCount")) {
                response.append("• 视频检测：").append(stats.get("videoCount")).append(" 条\n");
            }
            if (stats.containsKey("cameraCount")) {
                response.append("• 摄像头检测：").append(stats.get("cameraCount")).append(" 条\n");
            }
        } else {
            response.append("• 记录数量：").append(result.getTotalCount()).append(" 条\n");
        }

        response.append("\n📊 您可以继续询问更详细的统计信息，比如按作物类型、时间段等进行统计。");

        return response.toString();
    }

    /**
     * 生成报告操作的回复内容
     */
    private String generateReportResponse(RecordOperationResult result) {
        StringBuilder response = new StringBuilder();

        response.append("📊 分析报告生成成功！\n\n");

        if (result.getData() instanceof AnalysisReportDTO) {
            AnalysisReportDTO report = (AnalysisReportDTO) result.getData();

            response.append("📋 报告概览：\n");
            response.append("• 报告标题：").append(report.getTitle()).append("\n");
            response.append("• 作物类型：").append(report.getCropType() != null ? getCropDisplayName(report.getCropType()) : "全部作物").append("\n");
            response.append("• 分析记录：").append(report.getTotalRecords()).append(" 条\n");
            response.append("• 生成时间：").append(formatDateTime(report.getGenerateTime())).append("\n\n");

            // 执行摘要
            if (report.getExecutiveSummary() != null) {
                response.append("📈 关键指标：\n");
                if (report.getExecutiveSummary().getKeyMetrics() != null) {
                    Map<String, Object> metrics = report.getExecutiveSummary().getKeyMetrics();
                    if (metrics.containsKey("averageConfidence")) {
                        response.append("• 平均置信度：").append(metrics.get("averageConfidence")).append("%\n");
                    }
                    if (metrics.containsKey("diseaseTypes")) {
                        response.append("• 病害类型数：").append(metrics.get("diseaseTypes")).append(" 种\n");
                    }
                }
                response.append("\n");
            }

            // 病害分析
            if (report.getDiseaseAnalysis() != null) {
                response.append("🦠 病害分析：\n");
                response.append("• 最常见病害：").append(report.getDiseaseAnalysis().getMostCommonDisease()).append("\n");
                response.append("• 病害率：").append(report.getDiseaseAnalysis().getDiseaseRate()).append("%\n\n");
            }

            // 置信度分析
            if (report.getConfidenceAnalysis() != null) {
                response.append("🎯 置信度分析：\n");
                response.append("• 平均置信度：").append(report.getConfidenceAnalysis().getAverageConfidence()).append("%\n");
                response.append("• 高置信度比例：").append(report.getConfidenceAnalysis().getHighConfidenceRate()).append("%\n\n");
            }

            // 建议
            if (report.getRecommendations() != null && !report.getRecommendations().isEmpty()) {
                response.append("💡 主要建议：\n");
                for (int i = 0; i < Math.min(3, report.getRecommendations().size()); i++) {
                    response.append("• ").append(report.getRecommendations().get(i)).append("\n");
                }
                response.append("\n");
            }

            response.append("📄 完整的分析报告已生成，您可以在报告页面查看详细内容并导出为PDF、Excel等格式。");
        }

        return response.toString();
    }

    /**
     * 获取作物显示名称
     */
    private String getCropDisplayName(String cropType) {
        switch (cropType) {
            case "corn": return "玉米";
            case "rice": return "水稻";
            case "strawberry": return "草莓";
            case "tomato": return "西红柿";
            default: return cropType;
        }
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) return "";
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 生成删除操作的回复内容
     */
    private String generateDeleteResponse(RecordOperationResult result) {
        StringBuilder response = new StringBuilder();

        response.append("🗑️ 删除操作完成：\n");
        response.append("• 已删除：").append(result.getAffectedCount()).append(" 条记录\n");

        if (result.getDetails() != null && !result.getDetails().isEmpty()) {
            response.append("• 操作详情：\n");
            for (String detail : result.getDetails()) {
                response.append("  - ").append(detail).append("\n");
            }
        }

        response.append("\n⚠️ 注意：删除操作不可撤销，请确保这是您想要的结果。");

        return response.toString();
    }

    /**
     * 处理删除确认指令
     */
    private ChatResponse handleDeleteConfirmation(ChatRequest request, ChatMessage userMessage,
                                                 String sessionId, List<ChatMessage> history) {
        try {
            RecordQueryRequest pendingRequest = pendingDeleteOperations.get(sessionId);
            if (pendingRequest == null) {
                // 没有待确认的操作，按普通聊天处理
                return sendNormalChatMessage(request, userMessage, sessionId, history);
            }

            String aiContent;
            RecordOperationResult operationResult = null;

            if (commandParserService.isConfirmCommand(request.getMessage())) {
                // 确认删除
                log.info("用户确认删除操作，执行删除");
                pendingRequest.setNeedConfirmation(false);
                operationResult = recordManagementService.executeOperation(pendingRequest);

                if (operationResult.getSuccess()) {
                    aiContent = "✅ 删除操作已执行完成！\n\n" +
                        generateDeleteResponse(operationResult);
                } else {
                    aiContent = "❌ 删除操作执行失败：" + operationResult.getError();
                }

                // 清除待确认的操作
                pendingDeleteOperations.remove(sessionId);

            } else if (commandParserService.isCancelCommand(request.getMessage())) {
                // 取消删除
                log.info("用户取消删除操作");
                aiContent = "✅ 删除操作已取消。您的数据是安全的。\n\n" +
                    "如果您需要其他帮助，请随时告诉我！";

                // 清除待确认的操作
                pendingDeleteOperations.remove(sessionId);

            } else {
                // 不是确认或取消指令，重新提醒
                aiContent = "⚠️ 您还有一个待确认的删除操作。\n\n" +
                    "请明确回复：\n" +
                    "• 确认删除：'确认'、'是的'、'删除'\n" +
                    "• 取消删除：'取消'、'不要'、'算了'\n\n" +
                    "或者您可以重新输入新的指令。";
            }

            // 创建AI消息
            ChatMessage aiMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .content(aiContent)
                .role("assistant")
                .username("小虫博士")
                .timestamp(LocalDateTime.now())
                .status("sent")
                .sessionId(sessionId)
                .messageType("record_operation")
                .recordOperationResult(operationResult)
                .build();

            history.add(aiMessage);

            // 限制历史记录长度
            if (history.size() > 20) {
                history.subList(0, history.size() - 20).clear();
            }

            return ChatResponse.builder()
                .userMessage(userMessage)
                .aiMessage(aiMessage)
                .sessionId(sessionId)
                .success(true)
                .responseType("record_operation")
                .recordOperationResult(operationResult)
                .build();

        } catch (Exception e) {
            log.error("处理删除确认指令失败", e);

            // 清除待确认的操作
            pendingDeleteOperations.remove(sessionId);

            String errorContent = "抱歉，处理确认指令时出现了问题：" + e.getMessage() +
                "\n\n删除操作已取消，您的数据是安全的。";

            ChatMessage aiMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .content(errorContent)
                .role("assistant")
                .username("小虫博士")
                .timestamp(LocalDateTime.now())
                .status("sent")
                .sessionId(sessionId)
                .build();

            history.add(aiMessage);

            return ChatResponse.builder()
                .userMessage(userMessage)
                .aiMessage(aiMessage)
                .sessionId(sessionId)
                .success(true)
                .build();
        }
    }

    /**
     * 发送普通聊天消息（调用DeepSeek API）
     */
    private ChatResponse sendNormalChatMessage(ChatRequest request, ChatMessage userMessage,
                                             String sessionId, List<ChatMessage> history) {
        // 这里调用原来的DeepSeek API逻辑
        // 为了简化，这里返回一个基本的响应
        String aiContent = "我是小虫博士，您的农业害虫检测AI助手。我可以帮您管理检测记录，回答农业问题。\n\n" +
            "您可以尝试以下指令：\n" +
            "• 查看我的检测记录\n" +
            "• 统计检测数量\n" +
            "• 搜索特定作物的记录\n" +
            "• 删除指定条件的记录";

        ChatMessage aiMessage = ChatMessage.builder()
            .id(UUID.randomUUID().toString())
            .content(aiContent)
            .role("assistant")
            .username("小虫博士")
            .timestamp(LocalDateTime.now())
            .status("sent")
            .sessionId(sessionId)
            .build();

        history.add(aiMessage);

        return ChatResponse.builder()
            .userMessage(userMessage)
            .aiMessage(aiMessage)
            .sessionId(sessionId)
            .success(true)
            .build();
    }
}
