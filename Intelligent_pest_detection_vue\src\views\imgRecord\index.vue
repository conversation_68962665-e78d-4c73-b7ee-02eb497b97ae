<template>
	<div class="pest-page-container">
		<div class="pest-content-wrapper">
			<!-- 页面标题 -->
			<div class="page-header animate-fade-in-up">
				<h1 class="pest-title">图像检测记录</h1>
				<p class="pest-subtitle">查看和管理历史图像害虫检测记录</p>
			</div>

			<!-- 搜索过滤器 -->
			<div class="search-panel pest-card animate-fade-in-up animate-delay-100">
				<div class="search-header">
					<i class="iconfont icon-sousuo"></i>
					<span>搜索过滤</span>
				</div>
				<div class="search-content">
					<div class="search-row">
						<div class="search-item">
							<label class="search-label">作物类型</label>
							<el-input
								v-model="state.tableData.param.search1"
								placeholder="请输入农作物类型"
								size="large"
								class="search-input"
								clearable
							>
								<template #prefix>
									<i class="iconfont icon-zhiwu"></i>
								</template>
							</el-input>
						</div>
						<div class="search-item">
							<label class="search-label">识别结果</label>
							<el-input
								v-model="state.tableData.param.search2"
								placeholder="请输入识别结果"
								size="large"
								class="search-input"
								clearable
							>
								<template #prefix>
									<i class="iconfont icon-jieguo"></i>
								</template>
							</el-input>
						</div>
						<div class="search-item">
							<el-button
								type="primary"
								@click="getTableData()"
								class="pest-btn-primary search-button"
								size="large"
							>
								<i class="iconfont icon-sousuo" style="margin-right: 8px;"></i>
								搜索
							</el-button>
						</div>
					</div>
				</div>
			</div>

			<!-- 统计信息 -->
			<div class="stats-panel animate-fade-in-up animate-delay-200">
				<div class="stat-card pest-card-stats">
					<div class="stats-number">{{ state.tableData.total }}</div>
					<div class="stats-label">总记录数</div>
				</div>
				<div class="stat-card pest-card-stats">
					<div class="stats-number">{{ state.tableData.data.length }}</div>
					<div class="stats-label">当前页记录</div>
				</div>
				<div class="stat-card pest-card-stats">
					<div class="stats-number">{{ Math.ceil(state.tableData.total / state.tableData.param.pageSize) }}</div>
					<div class="stats-label">总页数</div>
				</div>
			</div>

			<!-- 记录列表 -->
			<div class="records-section animate-fade-in-up animate-delay-300">
				<div class="records-header">
					<div class="header-info">
						<i class="iconfont icon-liebiao"></i>
						<span>检测记录列表</span>
					</div>
					<div class="header-actions">
						<el-button
							type="info"
							@click="getTableData()"
							size="default"
						>
							<i class="iconfont icon-shuaxin" style="margin-right: 4px;"></i>
							刷新
						</el-button>
					</div>
				</div>

				<div class="records-grid" v-loading="state.tableData.loading">
					<div
						v-for="(record, index) in state.tableData.data"
						:key="record.id"
						class="record-card pest-card animate-fade-in-up"
						:style="{ animationDelay: `${0.1 * index}s` }"
					>
						<div class="record-header">
							<div class="record-number">
								<span class="number-label">#</span>
								<span class="number-value">{{ record.num }}</span>
							</div>
							<div class="record-actions">
								<el-button
									type="danger"
									size="small"
									@click="onRowDel(record)"
									plain
								>
									<el-icon><Delete /></el-icon>
									删除
								</el-button>
							</div>
						</div>

						<div class="record-images">
							<div class="image-section">
								<div class="image-label">原始图片</div>
								<div class="image-container" @click="previewImage(record.inputImg)">
									<img :src="record.inputImg" class="record-image" />
									<div class="image-overlay">
										<el-icon class="preview-icon"><ZoomIn /></el-icon>
									</div>
								</div>
							</div>
							<div class="image-arrow">
								<el-icon><ArrowRight /></el-icon>
							</div>
							<div class="image-section">
								<div class="image-label">检测结果</div>
								<div class="image-container" @click="previewImage(record.outImg)">
									<img :src="record.outImg" class="record-image" />
									<div class="image-overlay">
										<el-icon class="preview-icon"><ZoomIn /></el-icon>
									</div>
								</div>
							</div>
						</div>

						<div class="record-details">
							<div class="detail-row">
								<div class="detail-item">
									<span class="detail-label">检测模型</span>
									<span class="detail-value">{{ record.weight }}</span>
								</div>
								<div class="detail-item">
									<span class="detail-label">置信度阈值</span>
									<span class="detail-value">{{ record.conf }}</span>
								</div>
							</div>
							<div class="detail-row">
								<div class="detail-item">
									<span class="detail-label">处理时间</span>
									<span class="detail-value">{{ record.allTime }}</span>
								</div>
								<div class="detail-item">
									<span class="detail-label">检测用户</span>
									<span class="detail-value">{{ record.username }}</span>
								</div>
							</div>
							<div class="detail-row">
								<div class="detail-item full-width">
									<span class="detail-label">检测时间</span>
									<span class="detail-value">{{ record.startTime }}</span>
								</div>
							</div>
						</div>

						<div class="record-results" v-if="record.family && record.family.length > 0">
							<div class="results-header">
								<el-icon><Document /></el-icon>
								<span>检测结果详情</span>
							</div>
							<div class="results-list">
								<div
									v-for="(result, idx) in record.family"
									:key="idx"
									class="result-item"
								>
									<div class="result-label">{{ result.label }}</div>
									<div class="result-confidence">{{ result.confidence }}</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 分页 -->
				<div class="pagination-section">
					<el-pagination
						@size-change="onHandleSizeChange"
						@current-change="onHandleCurrentChange"
						:pager-count="5"
						:page-sizes="[10, 20, 30, 50]"
						v-model:current-page="state.tableData.param.pageNum"
						background
						v-model:page-size="state.tableData.param.pageSize"
						layout="total, sizes, prev, pager, next, jumper"
						:total="state.tableData.total"
						class="pest-pagination"
					/>
				</div>
			</div>
		</div>

		<!-- 图片预览对话框 -->
		<el-dialog
			v-model="imagePreviewVisible"
			title="图片预览"
			width="80%"
			center
			:close-on-click-modal="true"
		>
			<div class="image-preview-container">
				<img :src="previewImageUrl" class="preview-image" alt="预览图片" />
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="imagePreviewVisible = false">关闭</el-button>
					<el-button type="primary" @click="downloadImage">下载图片</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemRole">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Delete, ZoomIn, ArrowRight, Document } from '@element-plus/icons-vue';
import request from '/@/utils/request';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';

const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

const state = reactive({
	tableData: {
		data: [] as any,
		total: 0,
		loading: false,
		param: {
			search: '',
			search1: '',
			search2: '',
			pageNum: 1,
			pageSize: 10,
		},
	},
});

// 图片预览相关
const imagePreviewVisible = ref(false);
const previewImageUrl = ref('');

const getTableData = () => {
	state.tableData.loading = true;
	if (userInfos.value.userName != 'admin') {
		state.tableData.param.search = userInfos.value.userName;
	}
	request
		.get('/api/imgRecords', {
			params: state.tableData.param,
		})
		.then((res) => {
			if (res.code == 0) {
				state.tableData.data = [];
				setTimeout(() => {
					state.tableData.loading = false;
				}, 500);
				for (let i = 0; i < res.data.records.length; i++) {
					const confidences = JSON.parse(res.data.records[i].confidence);
					const labels = JSON.parse(res.data.records[i].label);
					const transformedData = transformData(res.data.records[i], confidences, labels);
					transformedData["num"] = i + 1;
					state.tableData.data[i] = transformedData
				}
				state.tableData.total = res.data.total;
			} else {
				ElMessage({
					type: 'error',
					message: res.msg,
				});
			}
		});
};

const transformData = (originalData, confidences, labels) => {
    const family = labels.map((label, index) => ({
        label: label,
        confidence: confidences[index],
        startTime: originalData.startTime
    }));

    const result = {
		id: originalData.id,
        inputImg: originalData.inputImg,
        outImg: originalData.outImg,
        weight: originalData.weight,
        allTime: originalData.allTime,
        conf: originalData.conf,
        startTime: originalData.startTime,
        username: originalData.username,
        family: family
    };

    return result;
}


// 删除
const onRowDel = (row: any) => {
	ElMessageBox.confirm(`此操作将永久删除该信息，是否继续?`, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			console.log(row);
			request.delete('/api/imgRecords/' + row.id).then((res) => {
				if (res.code == 0) {
					console.log(res.data);
					ElMessage({
						type: 'success',
						message: '删除成功！',
					});
				} else {
					ElMessage({
						type: 'error',
						message: res.msg,
					});
				}
			});
			setTimeout(() => {
				getTableData();
			}, 500);
		})
		.catch(() => { });
};
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.param.pageSize = val;
	getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
	state.tableData.param.pageNum = val;
	getTableData();
};

// 图片预览
const previewImage = (imageUrl: string) => {
	previewImageUrl.value = imageUrl;
	imagePreviewVisible.value = true;
};

// 下载图片
const downloadImage = () => {
	if (previewImageUrl.value) {
		const link = document.createElement('a');
		link.href = previewImageUrl.value;
		link.download = `pest_detection_${Date.now()}.jpg`;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		ElMessage.success('图片下载成功！');
	}
};

// 页面加载时
onMounted(() => {
	getTableData();
});
</script>

<style scoped lang="scss">
.page-header {
	text-align: center;
	margin-bottom: var(--spacing-xl);
}

.search-panel {
	margin-bottom: var(--spacing-xl);

	.search-header {
		display: flex;
		align-items: center;
		gap: var(--spacing-sm);
		margin-bottom: var(--spacing-lg);
		font-size: 1.125rem;
		font-weight: 600;
		color: var(--text-primary);

		i {
			color: var(--primary-color);
			font-size: 1.25rem;
		}
	}

	.search-content {
		.search-row {
			display: grid;
			grid-template-columns: 1fr 1fr auto;
			gap: var(--spacing-lg);
			align-items: end;
		}

		.search-item {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-sm);

			.search-label {
				font-size: 0.875rem;
				font-weight: 500;
				color: var(--text-secondary);
			}

			.search-input {
				:deep(.el-input__wrapper) {
					border: 2px solid var(--border-light);
					border-radius: var(--radius-md);
					transition: all var(--transition-normal);

					&:hover {
						border-color: var(--border-medium);
					}

					&.is-focus {
						border-color: var(--primary-light);
						box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
					}
				}
			}

			.search-button {
				height: 48px;
				padding: 0 var(--spacing-xl);
				font-weight: 600;
			}
		}
	}
}

.stats-panel {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: var(--spacing-lg);
	margin-bottom: var(--spacing-xl);
}

.records-section {
	.records-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: var(--spacing-lg);

		.header-info {
			display: flex;
			align-items: center;
			gap: var(--spacing-sm);
			font-size: 1.125rem;
			font-weight: 600;
			color: var(--text-primary);

			i {
				color: var(--primary-color);
				font-size: 1.25rem;
			}
		}
	}

	.records-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
		gap: var(--spacing-lg);
		margin-bottom: var(--spacing-xl);
	}
}

.record-card {
	.record-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: var(--spacing-lg);

		.record-number {
			display: flex;
			align-items: center;
			gap: var(--spacing-xs);

			.number-label {
				color: var(--text-muted);
				font-size: 0.875rem;
			}

			.number-value {
				font-size: 1.25rem;
				font-weight: 700;
				color: var(--primary-color);
			}
		}

		.record-actions {
			.el-button {
				color: var(--text-muted);
				border: 1px solid #f56565;
				background: rgba(245, 101, 101, 0.1);

				&:hover {
					color: white;
					background: #dc2626;
					border-color: #dc2626;
				}
			}
		}
	}

	.record-images {
		display: grid;
		grid-template-columns: 1fr auto 1fr;
		gap: var(--spacing-md);
		align-items: center;
		margin-bottom: var(--spacing-lg);

		.image-section {
			text-align: center;

			.image-label {
				font-size: 0.875rem;
				color: var(--text-secondary);
				margin-bottom: var(--spacing-sm);
				font-weight: 500;
			}

			.image-container {
				position: relative;
				border-radius: var(--radius-md);
				overflow: hidden;
				cursor: pointer;

				.record-image {
					width: 100%;
					height: 120px;
					object-fit: cover;
					transition: transform var(--transition-normal);
				}

				.image-overlay {
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background: rgba(0, 0, 0, 0.5);
					display: flex;
					align-items: center;
					justify-content: center;
					opacity: 0;
					transition: opacity var(--transition-normal);

					.preview-icon {
						color: white;
						font-size: 1.5rem;
					}

					i {
						color: white;
						font-size: 1.5rem;
					}
				}

				&:hover {
					.record-image {
						transform: scale(1.05);
					}

					.image-overlay {
						opacity: 1;
					}
				}
			}
		}

		.image-arrow {
			color: var(--primary-color);
			font-size: 1.25rem;
		}
	}

	.record-details {
		margin-bottom: var(--spacing-lg);

		.detail-row {
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: var(--spacing-md);
			margin-bottom: var(--spacing-sm);

			&:last-child {
				margin-bottom: 0;
			}
		}

		.detail-item {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-xs);

			&.full-width {
				grid-column: 1 / -1;
			}

			.detail-label {
				font-size: 0.75rem;
				color: var(--text-muted);
				font-weight: 500;
				text-transform: uppercase;
				letter-spacing: 0.5px;
			}

			.detail-value {
				font-size: 0.875rem;
				color: var(--text-primary);
				font-weight: 500;
			}
		}
	}

	.record-results {
		border-top: 1px solid var(--border-light);
		padding-top: var(--spacing-md);

		.results-header {
			display: flex;
			align-items: center;
			gap: var(--spacing-sm);
			margin-bottom: var(--spacing-md);
			font-size: 0.875rem;
			font-weight: 600;
			color: var(--text-primary);

			i {
				color: var(--secondary-color);
			}
		}

		.results-list {
			display: flex;
			flex-wrap: wrap;
			gap: var(--spacing-sm);

			.result-item {
				display: flex;
				align-items: center;
				gap: var(--spacing-xs);
				padding: var(--spacing-xs) var(--spacing-sm);
				background: var(--bg-secondary);
				border-radius: var(--radius-sm);
				font-size: 0.75rem;

				.result-label {
					color: var(--text-primary);
					font-weight: 500;
				}

				.result-confidence {
					color: var(--secondary-color);
					font-weight: 600;
				}
			}
		}
	}
}

.pagination-section {
	display: flex;
	justify-content: center;
	margin-top: var(--spacing-xl);

	.pest-pagination {
		:deep(.el-pagination) {
			.el-pager li {
				border-radius: var(--radius-sm);
				margin: 0 2px;

				&.is-active {
					background: var(--primary-gradient);
					color: white;
				}
			}

			.btn-prev, .btn-next {
				border-radius: var(--radius-sm);
			}
		}
	}
}

.image-preview-container {
	text-align: center;
	padding: var(--spacing-lg);

	.preview-image {
		max-width: 100%;
		max-height: 70vh;
		object-fit: contain;
		border-radius: var(--radius-lg);
		box-shadow: var(--shadow-lg);
		cursor: zoom-in;
		transition: transform var(--transition-normal);

		&:hover {
			transform: scale(1.02);
		}
	}
}

.dialog-footer {
	display: flex;
	gap: var(--spacing-md);
	justify-content: flex-end;
}

// 响应式设计
@media (max-width: 1024px) {
	.search-content .search-row {
		grid-template-columns: 1fr;
		gap: var(--spacing-md);
	}

	.stats-panel {
		grid-template-columns: 1fr;
	}

	.records-grid {
		grid-template-columns: 1fr;
	}
}

@media (max-width: 768px) {
	.record-images {
		grid-template-columns: 1fr;
		gap: var(--spacing-sm);

		.image-arrow {
			transform: rotate(90deg);
		}
	}

	.record-details .detail-row {
		grid-template-columns: 1fr;
	}
}
</style>
