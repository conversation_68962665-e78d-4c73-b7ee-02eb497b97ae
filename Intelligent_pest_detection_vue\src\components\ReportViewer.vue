<template>
  <div class="report-viewer">
    <!-- 报告头部 -->
    <div class="report-header" ref="reportHeader">
      <div class="header-content">
        <h1 class="report-title">{{ report.title }}</h1>
        <div class="report-meta">
          <el-tag type="info">{{ report.reportType }}</el-tag>
          <span class="generate-time">生成时间: {{ formatDateTime(report.generateTime) }}</span>
        </div>
        <div class="export-actions">
          <el-button-group>
            <el-button type="primary" @click="exportToPDF" :loading="exporting.pdf">
              <el-icon><Download /></el-icon>
              导出PDF
            </el-button>
            <el-button type="success" @click="exportToExcel" :loading="exporting.excel">
              <el-icon><Document /></el-icon>
              导出Excel
            </el-button>
            <el-button type="warning" @click="exportToJSON" :loading="exporting.json">
              <el-icon><Files /></el-icon>
              导出JSON
            </el-button>
            <el-button type="info" @click="exportToTXT" :loading="exporting.txt">
              <el-icon><Memo /></el-icon>
              导出TXT
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 报告内容 -->
    <div class="report-content" ref="reportContent">
      <!-- 执行摘要 -->
      <div class="report-section" v-if="report.executiveSummary">
        <h2 class="section-title">执行摘要</h2>
        <div class="summary-content">
          <p class="overview">{{ report.executiveSummary.overview }}</p>
          
          <!-- 关键指标 -->
          <div class="key-metrics">
            <h3>关键指标</h3>
            <el-row :gutter="20">
              <el-col :span="6" v-for="(value, key) in report.executiveSummary.keyMetrics" :key="key">
                <div class="metric-card">
                  <div class="metric-value">{{ formatMetricValue(value) }}</div>
                  <div class="metric-label">{{ formatMetricLabel(key) }}</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 亮点 -->
          <div class="highlights" v-if="report.executiveSummary.highlights?.length">
            <h3>主要亮点</h3>
            <ul>
              <li v-for="highlight in report.executiveSummary.highlights" :key="highlight">
                {{ highlight }}
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 病害分析 -->
      <div class="report-section" v-if="report.diseaseAnalysis">
        <h2 class="section-title">病害分析</h2>
        <div class="disease-content">
          <div class="disease-overview">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="stat-card">
                  <div class="stat-value">{{ report.diseaseAnalysis.mostCommonDisease }}</div>
                  <div class="stat-label">最常见病害</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-card">
                  <div class="stat-value">{{ report.diseaseAnalysis.diseaseRate }}%</div>
                  <div class="stat-label">病害率</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-card">
                  <div class="stat-value">{{ Object.keys(report.diseaseAnalysis.diseaseDistribution || {}).length }}</div>
                  <div class="stat-label">病害类型数</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 病害分布图表 -->
          <div class="chart-container">
            <h3>病害分布</h3>
            <div ref="diseaseChart" class="chart" style="height: 300px;"></div>
          </div>

          <!-- 病害详情表格 -->
          <div class="disease-details">
            <h3>病害详情</h3>
            <el-table :data="report.diseaseAnalysis.diseaseDetails" stripe>
              <el-table-column prop="diseaseName" label="病害名称" width="150" />
              <el-table-column prop="count" label="检测次数" width="100" />
              <el-table-column prop="percentage" label="占比(%)" width="100" />
              <el-table-column prop="avgConfidence" label="平均置信度(%)" width="120" />
              <el-table-column prop="severity" label="严重程度" width="100">
                <template #default="{ row }">
                  <el-tag :type="getSeverityType(row.severity)">{{ row.severity }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" show-overflow-tooltip />
            </el-table>
          </div>
        </div>
      </div>

      <!-- 置信度分析 -->
      <div class="report-section" v-if="report.confidenceAnalysis">
        <h2 class="section-title">置信度分析</h2>
        <div class="confidence-content">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-value">{{ report.confidenceAnalysis.averageConfidence }}%</div>
                <div class="stat-label">平均置信度</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card high">
                <div class="stat-value">{{ report.confidenceAnalysis.highConfidenceRate }}%</div>
                <div class="stat-label">高置信度比例</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card medium">
                <div class="stat-value">{{ report.confidenceAnalysis.mediumConfidenceRate }}%</div>
                <div class="stat-label">中置信度比例</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card low">
                <div class="stat-value">{{ report.confidenceAnalysis.lowConfidenceRate }}%</div>
                <div class="stat-label">低置信度比例</div>
              </div>
            </el-col>
          </el-row>

          <!-- 置信度分布图表 -->
          <div class="chart-container">
            <h3>置信度分布</h3>
            <div ref="confidenceChart" class="chart" style="height: 300px;"></div>
          </div>
        </div>
      </div>

      <!-- 趋势分析 -->
      <div class="report-section" v-if="report.trendAnalysis">
        <h2 class="section-title">趋势分析</h2>
        <div class="trend-content">
          <div class="trend-summary">
            <p><strong>趋势描述:</strong> {{ report.trendAnalysis.trend }}</p>
            <p><strong>季节性模式:</strong> {{ report.trendAnalysis.seasonalPattern }}</p>
          </div>

          <!-- 时间趋势图表 -->
          <div class="chart-container">
            <h3>检测频率趋势</h3>
            <div ref="trendChart" class="chart" style="height: 300px;"></div>
          </div>
        </div>
      </div>

      <!-- 建议和结论 -->
      <div class="report-section" v-if="report.recommendations?.length">
        <h2 class="section-title">建议和结论</h2>
        <div class="recommendations">
          <el-alert
            v-for="(recommendation, index) in report.recommendations"
            :key="index"
            :title="recommendation"
            type="info"
            :closable="false"
            style="margin-bottom: 10px;"
          />
        </div>
      </div>

      <!-- 详细数据 -->
      <div class="report-section" v-if="showDetailData && report.detailData?.length">
        <h2 class="section-title">
          详细数据
          <el-button type="text" @click="showDetailData = !showDetailData">
            {{ showDetailData ? '隐藏' : '显示' }}
          </el-button>
        </h2>
        <div class="detail-data">
          <el-table :data="report.detailData" stripe max-height="400">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="cropType" label="作物类型" width="100" />
            <el-table-column prop="recordType" label="记录类型" width="100" />
            <el-table-column prop="startTime" label="检测时间" width="150" />
            <el-table-column prop="diseases" label="检测病害" show-overflow-tooltip />
            <el-table-column prop="avgConfidence" label="平均置信度" width="120">
              <template #default="{ row }">
                {{ row.avgConfidence?.toFixed(2) }}%
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Document, Files, Memo } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import { saveAs } from 'file-saver'
import * as XLSX from 'xlsx'

// Props
interface Props {
  report: any
}

const props = defineProps<Props>()

// Refs
const reportHeader = ref<HTMLElement>()
const reportContent = ref<HTMLElement>()
const diseaseChart = ref<HTMLElement>()
const confidenceChart = ref<HTMLElement>()
const trendChart = ref<HTMLElement>()

// State
const showDetailData = ref(false)
const exporting = ref({
  pdf: false,
  excel: false,
  json: false,
  txt: false
})

// 图表实例
let diseaseChartInstance: echarts.ECharts | null = null
let confidenceChartInstance: echarts.ECharts | null = null
let trendChartInstance: echarts.ECharts | null = null

// Methods
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatMetricValue = (value: any) => {
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  return value
}

const formatMetricLabel = (key: string) => {
  const labels: Record<string, string> = {
    totalRecords: '总记录数',
    cropType: '作物类型',
    averageConfidence: '平均置信度(%)',
    diseaseTypes: '病害类型数'
  }
  return labels[key] || key
}

const getSeverityType = (severity: string) => {
  switch (severity) {
    case '高': return 'danger'
    case '中': return 'warning'
    case '低': return 'success'
    default: return 'info'
  }
}

// 初始化图表
const initCharts = async () => {
  await nextTick()

  // 病害分布饼图
  if (diseaseChart.value && props.report.diseaseAnalysis?.diseaseDistribution) {
    diseaseChartInstance = echarts.init(diseaseChart.value)
    const diseaseData = Object.entries(props.report.diseaseAnalysis.diseaseDistribution).map(([name, value]) => ({
      name,
      value
    }))

    diseaseChartInstance.setOption({
      title: {
        text: '病害分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '病害分布',
          type: 'pie',
          radius: '50%',
          data: diseaseData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }

  // 置信度分布柱状图
  if (confidenceChart.value && props.report.chartData?.confidenceDistribution) {
    confidenceChartInstance = echarts.init(confidenceChart.value)
    const confidenceData = props.report.chartData.confidenceDistribution

    confidenceChartInstance.setOption({
      title: {
        text: '置信度分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: Object.keys(confidenceData)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '数量',
          type: 'bar',
          data: Object.values(confidenceData),
          itemStyle: {
            color: function(params: any) {
              const colors = ['#5470c6', '#91cc75', '#fac858']
              return colors[params.dataIndex % colors.length]
            }
          }
        }
      ]
    })
  }

  // 时间趋势线图
  if (trendChart.value && props.report.trendAnalysis?.timePoints) {
    trendChartInstance = echarts.init(trendChart.value)
    const timePoints = props.report.trendAnalysis.timePoints

    trendChartInstance.setOption({
      title: {
        text: '检测频率趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: timePoints.map((point: any) => point.date)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '检测次数',
          type: 'line',
          data: timePoints.map((point: any) => point.count),
          smooth: true,
          itemStyle: {
            color: '#5470c6'
          }
        }
      ]
    })
  }
}

// 导出为PDF
const exportToPDF = async () => {
  exporting.value.pdf = true
  try {
    const element = reportContent.value
    if (!element) return

    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true
    })

    const imgData = canvas.toDataURL('image/png')
    const pdf = new jsPDF('p', 'mm', 'a4')

    const imgWidth = 210
    const pageHeight = 295
    const imgHeight = (canvas.height * imgWidth) / canvas.width
    let heightLeft = imgHeight

    let position = 0

    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
    heightLeft -= pageHeight

    while (heightLeft >= 0) {
      position = heightLeft - imgHeight
      pdf.addPage()
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight
    }

    const fileName = `${props.report.title}_${new Date().toISOString().slice(0, 10)}.pdf`
    pdf.save(fileName)

    ElMessage.success('PDF导出成功')
  } catch (error) {
    console.error('PDF导出失败:', error)
    ElMessage.error('PDF导出失败')
  } finally {
    exporting.value.pdf = false
  }
}

// 导出为Excel
const exportToExcel = () => {
  exporting.value.excel = true
  try {
    const workbook = XLSX.utils.book_new()

    // 摘要工作表
    const summaryData = [
      ['报告标题', props.report.title],
      ['生成时间', formatDateTime(props.report.generateTime)],
      ['作物类型', props.report.cropType],
      ['总记录数', props.report.totalRecords],
      ['查询条件', props.report.queryConditions]
    ]

    if (props.report.executiveSummary?.keyMetrics) {
      Object.entries(props.report.executiveSummary.keyMetrics).forEach(([key, value]) => {
        summaryData.push([formatMetricLabel(key), formatMetricValue(value)])
      })
    }

    const summaryWS = XLSX.utils.aoa_to_sheet(summaryData)
    XLSX.utils.book_append_sheet(workbook, summaryWS, '摘要')

    // 病害详情工作表
    if (props.report.diseaseAnalysis?.diseaseDetails) {
      const diseaseWS = XLSX.utils.json_to_sheet(props.report.diseaseAnalysis.diseaseDetails)
      XLSX.utils.book_append_sheet(workbook, diseaseWS, '病害详情')
    }

    // 详细数据工作表
    if (props.report.detailData) {
      const detailWS = XLSX.utils.json_to_sheet(props.report.detailData)
      XLSX.utils.book_append_sheet(workbook, detailWS, '详细数据')
    }

    const fileName = `${props.report.title}_${new Date().toISOString().slice(0, 10)}.xlsx`
    XLSX.writeFile(workbook, fileName)

    ElMessage.success('Excel导出成功')
  } catch (error) {
    console.error('Excel导出失败:', error)
    ElMessage.error('Excel导出失败')
  } finally {
    exporting.value.excel = false
  }
}

// 导出为JSON
const exportToJSON = () => {
  exporting.value.json = true
  try {
    const jsonData = JSON.stringify(props.report, null, 2)
    const blob = new Blob([jsonData], { type: 'application/json' })
    const fileName = `${props.report.title}_${new Date().toISOString().slice(0, 10)}.json`
    saveAs(blob, fileName)

    ElMessage.success('JSON导出成功')
  } catch (error) {
    console.error('JSON导出失败:', error)
    ElMessage.error('JSON导出失败')
  } finally {
    exporting.value.json = false
  }
}

// 导出为TXT
const exportToTXT = () => {
  exporting.value.txt = true
  try {
    let txtContent = `${props.report.title}\n`
    txtContent += `${'='.repeat(props.report.title.length)}\n\n`

    txtContent += `生成时间: ${formatDateTime(props.report.generateTime)}\n`
    txtContent += `作物类型: ${props.report.cropType}\n`
    txtContent += `总记录数: ${props.report.totalRecords}\n`
    txtContent += `查询条件: ${props.report.queryConditions}\n\n`

    // 执行摘要
    if (props.report.executiveSummary) {
      txtContent += `执行摘要\n--------\n`
      txtContent += `${props.report.executiveSummary.overview}\n\n`

      if (props.report.executiveSummary.keyMetrics) {
        txtContent += `关键指标:\n`
        Object.entries(props.report.executiveSummary.keyMetrics).forEach(([key, value]) => {
          txtContent += `- ${formatMetricLabel(key)}: ${formatMetricValue(value)}\n`
        })
        txtContent += '\n'
      }

      if (props.report.executiveSummary.highlights?.length) {
        txtContent += `主要亮点:\n`
        props.report.executiveSummary.highlights.forEach((highlight: string) => {
          txtContent += `- ${highlight}\n`
        })
        txtContent += '\n'
      }
    }

    // 病害分析
    if (props.report.diseaseAnalysis) {
      txtContent += `病害分析\n--------\n`
      txtContent += `最常见病害: ${props.report.diseaseAnalysis.mostCommonDisease}\n`
      txtContent += `病害率: ${props.report.diseaseAnalysis.diseaseRate}%\n\n`

      if (props.report.diseaseAnalysis.diseaseDetails?.length) {
        txtContent += `病害详情:\n`
        props.report.diseaseAnalysis.diseaseDetails.forEach((detail: any) => {
          txtContent += `- ${detail.diseaseName}: ${detail.count}次 (${detail.percentage}%, 置信度${detail.avgConfidence}%)\n`
          txtContent += `  描述: ${detail.description}\n`
        })
        txtContent += '\n'
      }
    }

    // 置信度分析
    if (props.report.confidenceAnalysis) {
      txtContent += `置信度分析\n----------\n`
      txtContent += `平均置信度: ${props.report.confidenceAnalysis.averageConfidence}%\n`
      txtContent += `高置信度比例: ${props.report.confidenceAnalysis.highConfidenceRate}%\n`
      txtContent += `中置信度比例: ${props.report.confidenceAnalysis.mediumConfidenceRate}%\n`
      txtContent += `低置信度比例: ${props.report.confidenceAnalysis.lowConfidenceRate}%\n\n`
    }

    // 趋势分析
    if (props.report.trendAnalysis) {
      txtContent += `趋势分析\n--------\n`
      txtContent += `趋势描述: ${props.report.trendAnalysis.trend}\n`
      txtContent += `季节性模式: ${props.report.trendAnalysis.seasonalPattern}\n\n`
    }

    // 建议和结论
    if (props.report.recommendations?.length) {
      txtContent += `建议和结论\n----------\n`
      props.report.recommendations.forEach((recommendation: string, index: number) => {
        txtContent += `${index + 1}. ${recommendation}\n`
      })
    }

    const blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8' })
    const fileName = `${props.report.title}_${new Date().toISOString().slice(0, 10)}.txt`
    saveAs(blob, fileName)

    ElMessage.success('TXT导出成功')
  } catch (error) {
    console.error('TXT导出失败:', error)
    ElMessage.error('TXT导出失败')
  } finally {
    exporting.value.txt = false
  }
}

// 生命周期
onMounted(() => {
  initCharts()
})

// 清理图表实例
const cleanup = () => {
  if (diseaseChartInstance) {
    diseaseChartInstance.dispose()
    diseaseChartInstance = null
  }
  if (confidenceChartInstance) {
    confidenceChartInstance.dispose()
    confidenceChartInstance = null
  }
  if (trendChartInstance) {
    trendChartInstance.dispose()
    trendChartInstance = null
  }
}

// 暴露清理方法
defineExpose({
  cleanup
})
</script>

<style scoped lang="scss">
.report-viewer {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .report-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;

      .report-title {
        font-size: 28px;
        font-weight: bold;
        margin: 0 0 10px 0;
      }

      .report-meta {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;

        .generate-time {
          font-size: 14px;
          opacity: 0.9;
        }
      }

      .export-actions {
        display: flex;
        justify-content: flex-end;
      }
    }
  }

  .report-content {
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;

    .report-section {
      margin-bottom: 40px;

      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e4e7ed;
      }
    }

    .summary-content {
      .overview {
        font-size: 16px;
        line-height: 1.6;
        color: #606266;
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
      }

      .key-metrics {
        margin-bottom: 20px;

        h3 {
          font-size: 18px;
          margin-bottom: 15px;
          color: #409eff;
        }

        .metric-card {
          text-align: center;
          padding: 20px;
          background: #fff;
          border: 1px solid #e4e7ed;
          border-radius: 6px;
          transition: all 0.3s;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
          }

          .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 5px;
          }

          .metric-label {
            font-size: 14px;
            color: #909399;
          }
        }
      }

      .highlights {
        h3 {
          font-size: 18px;
          margin-bottom: 15px;
          color: #409eff;
        }

        ul {
          list-style: none;
          padding: 0;

          li {
            padding: 8px 0;
            padding-left: 20px;
            position: relative;
            color: #606266;

            &:before {
              content: '•';
              color: #409eff;
              font-weight: bold;
              position: absolute;
              left: 0;
            }
          }
        }
      }
    }

    .disease-content {
      .disease-overview {
        margin-bottom: 30px;
      }

      .stat-card {
        text-align: center;
        padding: 20px;
        background: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #409eff;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
        }

        &.high .stat-value {
          color: #67c23a;
        }

        &.medium .stat-value {
          color: #e6a23c;
        }

        &.low .stat-value {
          color: #f56c6c;
        }
      }

      .chart-container {
        margin: 30px 0;

        h3 {
          font-size: 18px;
          margin-bottom: 15px;
          color: #409eff;
        }

        .chart {
          border: 1px solid #e4e7ed;
          border-radius: 6px;
        }
      }

      .disease-details {
        h3 {
          font-size: 18px;
          margin-bottom: 15px;
          color: #409eff;
        }
      }
    }

    .confidence-content {
      .stat-card {
        text-align: center;
        padding: 20px;
        background: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #409eff;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
        }

        &.high .stat-value {
          color: #67c23a;
        }

        &.medium .stat-value {
          color: #e6a23c;
        }

        &.low .stat-value {
          color: #f56c6c;
        }
      }

      .chart-container {
        margin: 30px 0;

        h3 {
          font-size: 18px;
          margin-bottom: 15px;
          color: #409eff;
        }

        .chart {
          border: 1px solid #e4e7ed;
          border-radius: 6px;
        }
      }
    }

    .trend-content {
      .trend-summary {
        margin-bottom: 30px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;

        p {
          margin: 10px 0;
          color: #606266;
          line-height: 1.6;
        }
      }

      .chart-container {
        h3 {
          font-size: 18px;
          margin-bottom: 15px;
          color: #409eff;
        }

        .chart {
          border: 1px solid #e4e7ed;
          border-radius: 6px;
        }
      }
    }

    .recommendations {
      .el-alert {
        margin-bottom: 10px;
      }
    }

    .detail-data {
      margin-top: 20px;
    }
  }
}

// 打印样式
@media print {
  .report-viewer {
    box-shadow: none;

    .export-actions {
      display: none !important;
    }
  }
}
</style>
