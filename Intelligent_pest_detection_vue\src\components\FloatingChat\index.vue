<template>
	<div class="floating-chat-container">
		<!-- 聊天触发按钮 -->
		<div
			v-if="!isOpen"
			class="chat-trigger-btn"
			:class="{ 'animate-pulse': unreadCount > 0, 'animate-avatar-breath': isOnline }"
			@click="openChat"
			:style="{ right: position.x + 'px', bottom: position.y + 'px' }"
		>
			<i class="iconfont icon-kefu"></i>
			<div
				class="chat-badge animate-badge-pulse"
				v-if="unreadCount > 0"
			>
				{{ unreadCount > 99 ? '99+' : unreadCount }}
			</div>
			<div class="online-indicator" v-if="isOnline"></div>
		</div>

		<!-- 聊天窗口 -->
		<div
			v-if="isOpen"
			class="floating-chat-window animate-window-open"
			:class="{
				'minimized': isMinimized,
				'offline': !isOnline,
				'loading': isLoading
			}"
			:style="{ right: position.x + 'px', bottom: position.y + 'px' }"
			@mousedown="startDrag"
		>
			<!-- 聊天头部 -->
			<div class="chat-header" ref="chatHeader">
				<div class="header-left">
					<div class="ai-avatar" :class="{ 'animate-avatar-breath': isOnline }">
						<i class="iconfont icon-jiqiren"></i>
						<div class="avatar-status" :class="{ 'online': isOnline, 'animate-connection-pulse': !isOnline }"></div>
					</div>
					<div class="ai-info">
						<div class="ai-name">小虫博士</div>
						<div class="ai-status" :class="{ 'online': isOnline }">
							<span class="status-dot" :class="{ 'online': isOnline }"></span>
							{{ isOnline ? '在线' : '离线' }}
							<span v-if="isTyping" class="typing-text">正在输入...</span>
						</div>
					</div>
				</div>
				<div class="header-actions">
					<button class="action-btn" @click="toggleMinimize" title="最小化">
						<i class="iconfont icon-zuixiaohua"></i>
					</button>
					<button class="action-btn" @click="closeChat" title="关闭">
						<i class="iconfont icon-guanbi"></i>
					</button>
				</div>
			</div>

			<!-- 聊天内容区域 -->
			<div v-if="!isMinimized" class="chat-content">
				<!-- 消息列表 -->
				<div class="message-list" ref="messageList">
					<div
						v-for="(message, index) in messages"
						:key="message.id"
						class="message-item animate-message-in"
						:class="{
							'user-message': message.role === 'user',
							'ai-message': message.role === 'assistant',
							'sending': message.status === 'sending',
							'error': message.status === 'error'
						}"
						:style="{ animationDelay: `${index * 0.1}s` }"
					>
						<div class="message-avatar">
							<i v-if="message.role === 'user'" class="iconfont icon-yonghu"></i>
							<i v-else class="iconfont icon-jiqiren"></i>
						</div>
						<div class="message-content">
							<div class="message-text" v-html="formatMessage(message.content)"></div>

							<!-- 记录操作结果展示 -->
							<div v-if="message.recordOperationResult && message.role === 'assistant'" class="record-operation-result">
								<RecordOperationDisplay
									:result="message.recordOperationResult"
									@send-message="handleRecordOperationMessage"
								/>
							</div>

							<!-- AI消息的打字指示器 -->
							<div v-if="message.role === 'assistant' && message.status === 'sending'" class="message-typing-indicator">
								<span class="typing-cursor">|</span>
							</div>
							<div class="message-meta">
								<span class="message-time">{{ formatTime(message.timestamp) }}</span>
								<div class="message-status" v-if="message.role === 'user'">
									<i v-if="message.status === 'sending'" class="iconfont icon-jiazai animate-spin"></i>
									<i v-else-if="message.status === 'sent'" class="status-icon sent">✓</i>
									<i v-else-if="message.status === 'error'" class="status-icon error" @click="retryMessage(message.id)">⚠</i>
								</div>
							</div>
						</div>
					</div>
					
					<!-- AI正在输入提示 -->
					<div v-if="isTyping" class="typing-indicator animate-fade-in">
						<div class="message-avatar">
							<i class="iconfont icon-jiqiren"></i>
						</div>
						<div class="typing-dots">
							<span class="animate-typing"></span>
							<span class="animate-typing" style="animation-delay: 0.2s;"></span>
							<span class="animate-typing" style="animation-delay: 0.4s;"></span>
						</div>
						<div class="typing-text">小虫博士正在思考...</div>
					</div>
				</div>

				<!-- 输入区域 -->
				<div class="chat-input-area">
					<div class="input-container">
						<el-input
							v-model="inputMessage"
							type="textarea"
							:rows="2"
							placeholder="请输入您的问题..."
							@keydown.enter.prevent="handleEnterKey"
							:disabled="isLoading"
							resize="none"
							class="chat-input"
						/>
						<button
							class="send-btn"
							:class="{ 'loading': isLoading, 'disabled': !inputMessage.trim() || isLoading }"
							@click="sendMessage"
							:disabled="!inputMessage.trim() || isLoading"
						>
							<i v-if="isLoading" class="iconfont icon-jiazai animate-spin"></i>
							<i v-else class="iconfont icon-fasong"></i>
						</button>
					</div>
					<div class="input-actions">
						<button class="action-link" @click="clearHistory" :disabled="isLoading">
							清空历史
						</button>
						<button class="action-link" @click="showQuickQuestions">
							常见问题
						</button>
						<button class="action-link" @click="showRecordCommands">
							记录管理
						</button>
						<button class="action-link" @click="toggleVoice" v-if="supportVoice">
							{{ voiceEnabled ? '关闭语音' : '开启语音' }}
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- 调整大小手柄 -->
		<div class="resize-handle" v-if="!isMinimized">
			<i class="el-icon-bottom-right"></i>
		</div>

		<!-- 快捷问题弹窗 -->
		<el-dialog
			v-model="quickQuestionsVisible"
			title="常见问题"
			width="400px"
			:modal="false"
		>
			<div class="quick-questions">
				<div
					v-for="question in quickQuestions"
					:key="question.id"
					class="question-item"
					@click="selectQuickQuestion(question.text)"
				>
					<i class="iconfont icon-wenhao"></i>
					<span>{{ question.text }}</span>
				</div>
			</div>
		</el-dialog>

		<!-- 记录管理命令弹窗 -->
		<el-dialog
			v-model="recordCommandsVisible"
			title="记录管理命令"
			width="500px"
			:modal="false"
		>
			<div class="record-commands">
				<div class="command-category">
					<h4><i class="iconfont icon-chaxun"></i> 查询记录</h4>
					<div
						v-for="command in queryCommands"
						:key="command.id"
						class="command-item"
						@click="selectRecordCommand(command.text)"
					>
						<span class="command-text">{{ command.text }}</span>
						<span class="command-desc">{{ command.desc }}</span>
					</div>
				</div>

				<div class="command-category">
					<h4><i class="iconfont icon-tongji"></i> 统计记录</h4>
					<div
						v-for="command in countCommands"
						:key="command.id"
						class="command-item"
						@click="selectRecordCommand(command.text)"
					>
						<span class="command-text">{{ command.text }}</span>
						<span class="command-desc">{{ command.desc }}</span>
					</div>
				</div>

				<div class="command-category">
					<h4><i class="iconfont icon-baogao"></i> 生成报告</h4>
					<div
						v-for="command in reportCommands"
						:key="command.id"
						class="command-item report-command"
						@click="selectRecordCommand(command.text)"
					>
						<span class="command-text">{{ command.text }}</span>
						<span class="command-desc">{{ command.desc }}</span>
					</div>
				</div>

				<div class="command-category">
					<h4><i class="iconfont icon-shanchu"></i> 删除记录</h4>
					<div
						v-for="command in deleteCommands"
						:key="command.id"
						class="command-item delete-command"
						@click="selectRecordCommand(command.text)"
					>
						<span class="command-text">{{ command.text }}</span>
						<span class="command-desc">{{ command.desc }}</span>
					</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { useUserInfo } from '/@/stores/userInfo';
import { useChatStore } from '/@/stores/chat';
import { storeToRefs } from 'pinia';
import { ChatUtils } from '/@/api/chat';
import RecordOperationDisplay from './RecordOperationDisplay.vue';

// 路由
const router = useRouter();

// 用户信息
const userStore = useUserInfo();
const { userInfos } = storeToRefs(userStore);

// 聊天状态管理
const chatStore = useChatStore();
const {
	isOpen,
	isMinimized,
	isLoading,
	isTyping,
	isOnline,
	unreadCount,
	messages,
	position
} = storeToRefs(chatStore);

// 组件本地状态
const inputMessage = ref('');
const quickQuestionsVisible = ref(false);
const recordCommandsVisible = ref(false);
const voiceEnabled = ref(false);
const supportVoice = ref(false);

// 拖拽状态
const isDragging = ref(false);
const dragOffset = reactive({ x: 0, y: 0 });

// DOM引用
const messageList = ref<HTMLElement>();
const chatHeader = ref<HTMLElement>();

// 快捷问题
const quickQuestions = ref([
	{ id: 1, text: '如何使用图像检测功能？' },
	{ id: 2, text: '支持检测哪些作物病害？' },
	{ id: 3, text: '如何提高检测准确率？' },
	{ id: 4, text: '检测结果如何解读？' },
	{ id: 5, text: '系统支持哪些图片格式？' },
	{ id: 6, text: '如何查看历史检测记录？' }
]);

// 记录管理命令
const queryCommands = ref([
	{ id: 1, text: '查看我的图像检测记录', desc: '显示当前用户的所有图像检测记录' },
	{ id: 2, text: '搜索玉米相关的记录', desc: '查找玉米作物的检测记录' },
	{ id: 3, text: '显示今天的检测记录', desc: '查看今日的所有检测记录' },
	{ id: 4, text: '查找置信度大于80%的记录', desc: '筛选高置信度的检测结果' },
	{ id: 5, text: '查看最近10条检测记录', desc: '显示最新的检测记录' },
	{ id: 6, text: '搜索草莓病害检测记录', desc: '查找草莓相关的病害检测' }
]);

const countCommands = ref([
	{ id: 1, text: '统计本周的检测数量', desc: '计算本周总检测次数' },
	{ id: 2, text: '统计本月的图像检测记录', desc: '统计本月图像检测数量' },
	{ id: 3, text: '计算草莓检测记录有多少条', desc: '统计草莓检测记录总数' },
	{ id: 4, text: '统计置信度大于90%的记录数量', desc: '计算高置信度记录数' }
]);

const reportCommands = ref([
	{ id: 1, text: '生成玉米检测分析报告', desc: '分析玉米作物的检测记录并生成专业报告' },
	{ id: 2, text: '生成水稻病害分析报告', desc: '分析水稻病害检测数据并生成报告' },
	{ id: 3, text: '生成草莓检测趋势分析', desc: '分析草莓检测记录的时间趋势' },
	{ id: 4, text: '生成西红柿详细分析报告', desc: '生成西红柿检测的详细分析报告' },
	{ id: 5, text: '生成本周检测摘要报告', desc: '汇总本周所有检测记录的分析报告' },
	{ id: 6, text: '生成高置信度检测分析报告', desc: '分析置信度较高的检测记录' }
]);

const deleteCommands = ref([
	{ id: 1, text: '删除昨天的记录', desc: '清除昨日的检测记录' },
	{ id: 2, text: '清除置信度低于50%的记录', desc: '删除低置信度的检测结果' },
	{ id: 3, text: '删除本周的测试记录', desc: '清理本周的测试数据' },
	{ id: 4, text: '清空所有图像检测记录', desc: '删除所有图像检测历史' }
]);

// 打开聊天
const openChat = () => {
	chatStore.openChat();
};

// 关闭聊天
const closeChat = () => {
	chatStore.closeChat();
};

// 切换最小化
const toggleMinimize = () => {
	chatStore.toggleMinimize();
};

// 发送消息
const sendMessage = async () => {
	if (!inputMessage.value.trim() || isLoading.value) return;

	const messageContent = inputMessage.value.trim();
	const username = userInfos.value.userName || '用户';

	// 清空输入框
	inputMessage.value = '';

	// 调用store发送消息
	const success = await chatStore.sendMessage(messageContent, username);

	if (success) {
		// 滚动到底部
		scrollToBottom();
	}
};

// 处理回车键
const handleEnterKey = (event: KeyboardEvent) => {
	if (event.shiftKey) {
		// Shift+Enter 换行
		return;
	} else {
		// Enter 发送消息
		event.preventDefault();
		sendMessage();
	}
};

// 格式化消息内容
const formatMessage = (content: string) => {
	return ChatUtils.formatMessage(content);
};

// 格式化时间
const formatTime = (timestamp: string) => {
	return ChatUtils.formatTime(timestamp);
};

// 滚动到底部
const scrollToBottom = () => {
	nextTick(() => {
		if (messageList.value) {
			messageList.value.scrollTop = messageList.value.scrollHeight;
		}
	});
};

// 清空历史
const clearHistory = async () => {
	await chatStore.clearHistory();
};

// 显示快捷问题
const showQuickQuestions = () => {
	quickQuestionsVisible.value = true;
};

// 选择快捷问题
const selectQuickQuestion = (question: string) => {
	inputMessage.value = question;
	quickQuestionsVisible.value = false;
	sendMessage();
};

// 显示记录管理命令
const showRecordCommands = () => {
	recordCommandsVisible.value = true;
};

// 选择记录管理命令
const selectRecordCommand = (command: string) => {
	inputMessage.value = command;
	recordCommandsVisible.value = false;
	sendMessage();
};

// 处理记录操作消息
const handleRecordOperationMessage = (message: string) => {
	inputMessage.value = message;
	sendMessage();
};

// 重试发送消息
const retryMessage = async (messageId: string) => {
	const success = await chatStore.retryMessage(messageId);
	if (success) {
		ElMessage.success('消息重发成功');
		scrollToBottom();
	}
};



// 语音功能
const toggleVoice = () => {
	voiceEnabled.value = !voiceEnabled.value;
	ElMessage.info(voiceEnabled.value ? '语音功能已开启' : '语音功能已关闭');
};

// 检查语音支持
const checkVoiceSupport = () => {
	supportVoice.value = 'speechSynthesis' in window && 'SpeechSynthesisUtterance' in window;
};

// 语音播报
const speakMessage = (text: string) => {
	if (voiceEnabled.value && supportVoice.value) {
		try {
			const utterance = new SpeechSynthesisUtterance(text);
			utterance.lang = 'zh-CN';
			utterance.rate = 0.9;
			utterance.pitch = 1;
			utterance.volume = 0.8;
			speechSynthesis.speak(utterance);
		} catch (error) {
			console.warn('语音播报失败:', error);
		}
	}
};

// 拖拽功能
const startDrag = (event: MouseEvent) => {
	if (event.target === chatHeader.value || chatHeader.value?.contains(event.target as Node)) {
		isDragging.value = true;
		dragOffset.x = event.clientX - position.value.x;
		dragOffset.y = event.clientY - position.value.y;

		document.addEventListener('mousemove', onDrag);
		document.addEventListener('mouseup', stopDrag);
	}
};

const onDrag = (event: MouseEvent) => {
	if (isDragging.value) {
		const newX = Math.max(0, Math.min(window.innerWidth - 320, event.clientX - dragOffset.x));
		const newY = Math.max(0, Math.min(window.innerHeight - 400, event.clientY - dragOffset.y));
		chatStore.setPosition(newX, newY);
	}
};

const stopDrag = () => {
	isDragging.value = false;
	document.removeEventListener('mousemove', onDrag);
	document.removeEventListener('mouseup', stopDrag);
};

// 监听窗口大小变化
const handleResize = () => {
	const newX = Math.max(0, Math.min(window.innerWidth - 320, position.value.x));
	const newY = Math.max(0, Math.min(window.innerHeight - 400, position.value.y));
	chatStore.setPosition(newX, newY);
};

// 组件挂载
onMounted(() => {
	// 初始化聊天
	chatStore.initChat();

	// 检查服务健康状态
	chatStore.checkHealth();

	// 检查语音支持
	checkVoiceSupport();

	// 监听窗口大小变化
	window.addEventListener('resize', handleResize);

	// 监听键盘快捷键
	document.addEventListener('keydown', handleKeyboardShortcuts);

	// 监听来自iframe的消息（查看报告按钮点击）
	window.addEventListener('message', handleReportViewMessage);

	// 添加全局点击监听，用于调试
	document.addEventListener('click', (event) => {
		const target = event.target as HTMLElement;
		if (target && target.classList.contains('btn-view-report')) {
			console.log('检测到报告按钮点击');
			event.preventDefault();
			handleReportViewMessage({ data: { type: 'VIEW_REPORT' } });
		}
	});
});

// 处理查看报告消息
const handleReportViewMessage = (event: any) => {
	if (event.data && event.data.type === 'VIEW_REPORT') {
		console.log('收到查看报告消息:', event.data);

		// 查找最近的报告数据
		const lastMessage = messages.value[messages.value.length - 1];
		let reportData = null;

		if (lastMessage && lastMessage.recordOperationResult && lastMessage.recordOperationResult.data) {
			reportData = lastMessage.recordOperationResult.data;
		}

		// 将报告数据存储到sessionStorage，供报告页面使用
		if (reportData) {
			sessionStorage.setItem('currentReport', JSON.stringify(reportData));
			console.log('报告数据已存储:', reportData);
		}

		// 跳转到报告页面
		router.push('/reports').then(() => {
			console.log('已跳转到报告页面');
			// 关闭聊天窗口
			closeChat();
		}).catch(error => {
			console.error('跳转失败:', error);
			ElMessage.error('跳转到报告页面失败');
		});
	}
};

// 键盘快捷键
const handleKeyboardShortcuts = (event: KeyboardEvent) => {
	// Ctrl/Cmd + K 打开聊天
	if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
		event.preventDefault();
		if (!isOpen.value) {
			openChat();
		}
	}

	// Escape 关闭聊天
	if (event.key === 'Escape' && isOpen.value) {
		closeChat();
	}
};

// 监听消息变化，自动滚动
watch(() => messages.value.length, (newLength, oldLength) => {
	scrollToBottom();

	// 如果有新消息且是AI回复，播放语音
	if (newLength > oldLength) {
		const lastMessage = messages.value[messages.value.length - 1];
		if (lastMessage && lastMessage.role === 'assistant') {
			// 延迟播放语音
			setTimeout(() => {
				speakMessage(lastMessage.content.replace(/<[^>]*>/g, ''));
			}, 500);
		}
	}
});

// 监听在线状态变化
watch(() => isOnline.value, (newStatus) => {
	if (newStatus) {
		ElMessage.success('聊天服务已连接');
	} else {
		ElMessage.warning('聊天服务连接中断');
	}
});

// 监听打字状态
watch(() => isTyping.value, (typing) => {
	if (typing) {
		scrollToBottom();
	}
});
</script>

<style scoped lang="scss">
@import './chat-animations.scss';

.floating-chat-container {
	position: fixed;
	z-index: 9999;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// 聊天触发按钮
.chat-trigger-btn {
	position: fixed;
	width: 60px;
	height: 60px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
	transition: all 0.3s ease;

	&:hover {
		transform: scale(1.1);
		box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
	}

	i {
		font-size: 24px;
		color: white;
	}

	.chat-badge {
		position: absolute;
		top: -5px;
		right: -5px;
		background: #ff4757;
		color: white;
		border-radius: 50%;
		min-width: 20px;
		height: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 11px;
		font-weight: bold;
		padding: 0 4px;
		box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
	}

	.online-indicator {
		position: absolute;
		bottom: 2px;
		right: 2px;
		width: 12px;
		height: 12px;
		background: #2ed573;
		border: 2px solid white;
		border-radius: 50%;
		animation: pulse 2s infinite;
	}
}

// 聊天窗口
.floating-chat-window {
	position: fixed;
	width: 320px;
	height: 450px;
	min-width: 280px;
	min-height: 300px;
	max-width: 600px;
	max-height: 800px;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 12px;
	box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
	display: flex;
	flex-direction: column;
	overflow: hidden;
	transition: all 0.3s ease;
	border: 1px solid rgba(255, 255, 255, 0.3);
	resize: both;

	&.minimized {
		height: 60px;
	}

	&.offline {
		opacity: 0.9;
		background: rgba(245, 245, 245, 0.95);

		.chat-header {
			background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
		}
	}

	&.loading {
		.chat-header::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 2px;
			background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
			animation: loading-bar 1.5s infinite;
		}
	}
}

// 调整大小手柄
.resize-handle {
	position: absolute;
	bottom: 0;
	right: 0;
	width: 20px;
	height: 20px;
	background: rgba(102, 126, 234, 0.1);
	cursor: nw-resize;
	display: flex;
	align-items: center;
	justify-content: center;
	border-top-left-radius: 4px;
	transition: background 0.2s;

	&:hover {
		background: rgba(102, 126, 234, 0.2);
	}

	i {
		font-size: 12px;
		color: rgba(102, 126, 234, 0.6);
		transform: rotate(45deg);
	}
}

// 聊天头部
.chat-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 12px 16px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	cursor: move;
	user-select: none;

	.header-left {
		display: flex;
		align-items: center;
		gap: 10px;

		.ai-avatar {
			width: 36px;
			height: 36px;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;

			i {
				font-size: 18px;
			}

			.avatar-status {
				position: absolute;
				bottom: -2px;
				right: -2px;
				width: 12px;
				height: 12px;
				border-radius: 50%;
				border: 2px solid white;

				&.online {
					background: #2ed573;
				}

				&:not(.online) {
					background: #ff4757;
				}
			}
		}

		.ai-info {
			.ai-name {
				font-weight: 600;
				font-size: 14px;
			}

			.ai-status {
				font-size: 12px;
				opacity: 0.8;
				display: flex;
				align-items: center;
				gap: 4px;

				.status-dot {
					width: 6px;
					height: 6px;
					border-radius: 50%;
					background: #ff4757;

					&.online {
						background: #2ed573;
					}
				}

				.typing-text {
					color: #2ed573;
					font-style: italic;
					animation: pulse 1.5s infinite;
				}
			}
		}
	}

	.header-actions {
		display: flex;
		gap: 8px;

		.action-btn {
			background: none;
			border: none;
			color: white;
			cursor: pointer;
			padding: 4px;
			border-radius: 4px;
			transition: background 0.2s;

			&:hover {
				background: rgba(255, 255, 255, 0.2);
			}

			i {
				font-size: 14px;
			}
		}
	}
}

// 聊天内容
.chat-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	height: calc(100% - 60px);
}

// 消息列表
.message-list {
	flex: 1;
	overflow-y: auto;
	padding: 16px;
	display: flex;
	flex-direction: column;
	gap: 12px;

	&::-webkit-scrollbar {
		width: 4px;
	}

	&::-webkit-scrollbar-track {
		background: #f1f1f1;
	}

	&::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 2px;
	}
}

// 消息项
.message-item {
	display: flex;
	gap: 8px;
	margin-bottom: 12px;

	&.sending {
		opacity: 0.7;

		.message-content {
			position: relative;

			&::after {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
				animation: shimmer 1.5s infinite;
				border-radius: inherit;
			}
		}
	}

	&.error {
		.message-content {
			border: 1px solid #ff4757;
			background: #fff5f5;
		}
	}

	&.user-message {
		flex-direction: row-reverse;

		.message-content {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			border-radius: 18px 18px 4px 18px;
			box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
		}
	}

	&.ai-message {
		.message-content {
			background: #f8f9fa;
			color: #333;
			border-radius: 18px 18px 18px 4px;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		}
	}

	.message-avatar {
		width: 32px;
		height: 32px;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #e9ecef;
		flex-shrink: 0;

		i {
			font-size: 16px;
			color: #6c757d;
		}
	}

	.message-content {
		max-width: 70%;
		padding: 10px 14px;
		position: relative;

		.message-text {
			line-height: 1.5;
			font-size: 14px;
			word-wrap: break-word;

			:deep(strong) {
				font-weight: 600;
			}

			:deep(em) {
				font-style: italic;
				color: rgba(0, 0, 0, 0.8);
			}

			:deep(code) {
				background: rgba(0, 0, 0, 0.1);
				padding: 2px 4px;
				border-radius: 3px;
				font-family: 'Courier New', monospace;
				font-size: 13px;
			}

			:deep(.report-action-buttons) {
				margin-top: 10px;

				.btn-view-report {
					background: linear-gradient(135deg, #2ed573 0%, #26d466 100%);
					color: white;
					border: none;
					padding: 8px 16px;
					border-radius: 6px;
					cursor: pointer;
					font-size: 13px;
					font-weight: 500;
					transition: all 0.3s ease;
					box-shadow: 0 2px 8px rgba(46, 213, 115, 0.3);

					&:hover {
						transform: translateY(-1px);
						box-shadow: 0 4px 12px rgba(46, 213, 115, 0.4);
					}

					&:active {
						transform: translateY(0);
					}
				}
			}
		}

		.message-meta {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 6px;

			.message-time {
				font-size: 11px;
				opacity: 0.6;
			}

			.message-status {
				display: flex;
				align-items: center;
				gap: 4px;

				.status-icon {
					font-size: 12px;
					cursor: pointer;

					&.sent {
						color: #2ed573;
					}

					&.error {
						color: #ff4757;
						animation: shake 0.5s ease-in-out;

						&:hover {
							transform: scale(1.2);
						}
					}
				}
			}
		}
	}

	.message-status {
		display: flex;
		align-items: flex-end;

		.loading {
			animation: spin 1s linear infinite;
		}
	}
}

// AI输入提示
.typing-indicator {
	display: flex;
	gap: 8px;
	align-items: center;
	margin-bottom: 12px;

	.message-avatar {
		width: 32px;
		height: 32px;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #e9ecef;

		i {
			font-size: 16px;
			color: #6c757d;
		}
	}

	.typing-dots {
		background: #f8f9fa;
		border-radius: 18px;
		padding: 10px 14px;
		display: flex;
		gap: 4px;
		align-items: center;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

		span {
			width: 6px;
			height: 6px;
			background: #6c757d;
			border-radius: 50%;
		}
	}

	.typing-text {
		font-size: 12px;
		color: #6c757d;
		font-style: italic;
		margin-left: 8px;
	}
}

// 输入区域
.chat-input-area {
	border-top: 1px solid #e9ecef;
	padding: 12px;

	.input-container {
		display: flex;
		gap: 8px;
		align-items: flex-end;

		.chat-input {
			flex: 1;

			:deep(.el-textarea__inner) {
				border: 1px solid #e9ecef;
				border-radius: 8px;
				padding: 8px 12px;
				font-size: 14px;
				resize: none;

				&:focus {
					border-color: #667eea;
					box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
				}
			}
		}

		.send-btn {
			width: 36px;
			height: 36px;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border: none;
			border-radius: 8px;
			color: white;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.2s;
			position: relative;
			overflow: hidden;

			&::before {
				content: '';
				position: absolute;
				top: 50%;
				left: 50%;
				width: 0;
				height: 0;
				background: rgba(255, 255, 255, 0.3);
				border-radius: 50%;
				transition: all 0.3s;
				transform: translate(-50%, -50%);
			}

			&:hover:not(:disabled) {
				transform: scale(1.05);
				box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);

				&::before {
					width: 100%;
					height: 100%;
				}
			}

			&:active:not(:disabled) {
				transform: scale(0.95);
			}

			&.disabled {
				opacity: 0.5;
				cursor: not-allowed;
				background: #95a5a6;
			}

			&.loading {
				background: #95a5a6;
				cursor: not-allowed;
			}
		}
	}

	.input-actions {
		display: flex;
		justify-content: space-between;
		margin-top: 8px;
		flex-wrap: wrap;
		gap: 8px;

		.action-link {
			background: none;
			border: none;
			color: #667eea;
			font-size: 12px;
			cursor: pointer;
			padding: 4px 8px;
			border-radius: 4px;
			transition: all 0.2s;
			display: flex;
			align-items: center;
			gap: 4px;

			&:hover:not(:disabled) {
				background: rgba(102, 126, 234, 0.1);
				transform: translateY(-1px);
			}

			&:disabled {
				opacity: 0.5;
				cursor: not-allowed;
			}


		}
	}
}

// 快捷问题
.quick-questions {
	.question-item {
		display: flex;
		align-items: center;
		gap: 8px;
		padding: 12px;
		border-radius: 8px;
		cursor: pointer;
		transition: background 0.2s;

		&:hover {
			background: #f8f9fa;
		}

		i {
			color: #667eea;
			font-size: 16px;
		}

		span {
			font-size: 14px;
			color: #333;
		}
	}
}

// 记录管理命令样式
.record-commands {
	.command-category {
		margin-bottom: 20px;

		h4 {
			display: flex;
			align-items: center;
			margin: 0 0 12px 0;
			color: #1e293b;
			font-size: 16px;
			font-weight: 600;

			i {
				margin-right: 8px;
				color: #667eea;
				font-size: 18px;
			}
		}

		.command-item {
			display: flex;
			flex-direction: column;
			padding: 12px;
			margin-bottom: 8px;
			background: #f8fafc;
			border-radius: 8px;
			cursor: pointer;
			transition: all 0.2s ease;
			border-left: 3px solid transparent;

			&:hover {
				background: #e2e8f0;
				transform: translateX(4px);
				border-left-color: #667eea;
			}

			&.report-command {
				background: #f0fdf4;

				&:hover {
					background: #dcfce7;
					border-left-color: #22c55e;
				}

				.command-text {
					color: #16a34a;
				}
			}

			&.delete-command {
				background: #fef2f2;

				&:hover {
					background: #fee2e2;
					border-left-color: #ef4444;
				}

				.command-text {
					color: #dc2626;
				}
			}

			.command-text {
				color: #1e293b;
				font-size: 14px;
				font-weight: 500;
				margin-bottom: 4px;
			}

			.command-desc {
				color: #64748b;
				font-size: 12px;
				line-height: 1.4;
			}
		}
	}
}

// 动画
@keyframes spin {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

@keyframes typing {
	0%, 60%, 100% {
		transform: translateY(0);
		opacity: 0.5;
	}
	30% {
		transform: translateY(-10px);
		opacity: 1;
	}
}

@keyframes shimmer {
	0% {
		transform: translateX(-100%);
	}
	100% {
		transform: translateX(100%);
	}
}

@keyframes loading-bar {
	0% {
		transform: translateX(-100%);
	}
	100% {
		transform: translateX(100%);
	}
}

@keyframes pulse {
	0%, 100% {
		opacity: 1;
		transform: scale(1);
	}
	50% {
		opacity: 0.7;
		transform: scale(1.05);
	}
}

@keyframes shake {
	0%, 100% { transform: translateX(0); }
	10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
	20%, 40%, 60%, 80% { transform: translateX(2px); }
}

// 打字指示器样式
.message-typing-indicator {
	display: inline-block;
	margin-left: 2px;

	.typing-cursor {
		color: #667eea;
		font-weight: bold;
		animation: blink 1s infinite;
	}
}

@keyframes blink {
	0%, 50% { opacity: 1; }
	51%, 100% { opacity: 0; }
}

// 响应式
@media (max-width: 768px) {
	.floating-chat-window {
		width: 280px;
		height: 400px;
		right: 10px !important;
		bottom: 10px !important;
	}

	.chat-trigger-btn {
		right: 15px !important;
		bottom: 15px !important;
	}
}
</style>
