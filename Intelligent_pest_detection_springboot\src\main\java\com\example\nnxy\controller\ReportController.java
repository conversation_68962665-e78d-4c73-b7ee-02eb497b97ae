package com.example.nnxy.controller;

import com.example.nnxy.common.Result;
import com.example.nnxy.dto.AnalysisReportDTO;
import com.example.nnxy.dto.RecordQueryRequest;
import com.example.nnxy.service.ReportAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 报告控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/reports")
@CrossOrigin(origins = "*")
public class ReportController {
    
    @Autowired
    private ReportAnalysisService reportAnalysisService;
    
    /**
     * 生成分析报告
     */
    @PostMapping("/generate")
    public Result<AnalysisReportDTO> generateReport(@RequestBody ReportGenerateRequest request) {
        try {
            log.info("收到报告生成请求: {}", request);
            
            // 转换为RecordQueryRequest
            RecordQueryRequest queryRequest = convertToQueryRequest(request);
            
            // 生成报告
            AnalysisReportDTO report = reportAnalysisService.generateAnalysisReport(queryRequest);
            
            return Result.success(report);
            
        } catch (Exception e) {
            log.error("生成报告失败", e);
            return Result.error("500", "生成报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取报告历史（这里简化实现，实际可以存储到数据库）
     */
    @GetMapping("/history")
    public Result<List<AnalysisReportDTO>> getReportHistory() {
        try {
            // 这里返回空列表，实际项目中可以从数据库获取
            List<AnalysisReportDTO> history = new ArrayList<>();
            return Result.success(history);
        } catch (Exception e) {
            log.error("获取报告历史失败", e);
            return Result.error("500", "获取报告历史失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除报告（这里简化实现）
     */
    @DeleteMapping("/{reportId}")
    public Result<Void> deleteReport(@PathVariable String reportId) {
        try {
            log.info("删除报告: {}", reportId);
            // 这里简化实现，实际项目中需要从数据库删除
            return Result.success();
        } catch (Exception e) {
            log.error("删除报告失败", e);
            return Result.error("500", "删除报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 导出报告（这里简化实现，实际导出由前端处理）
     */
    @GetMapping("/{reportId}/export/{format}")
    public ResponseEntity<String> exportReport(@PathVariable String reportId, @PathVariable String format) {
        try {
            log.info("导出报告: {} 格式: {}", reportId, format);
            
            // 这里返回简单的文本，实际项目中可以根据格式生成相应的文件
            String content = "报告导出功能由前端处理";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_PLAIN);
            headers.setContentDispositionFormData("attachment", "report." + format);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(content);
                    
        } catch (Exception e) {
            log.error("导出报告失败", e);
            return ResponseEntity.badRequest().body("导出报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 转换报告生成请求为查询请求
     */
    private RecordQueryRequest convertToQueryRequest(ReportGenerateRequest request) {
        RecordQueryRequest queryRequest = new RecordQueryRequest();
        
        // 设置操作类型为报告生成
        queryRequest.setOperationType("REPORT");
        queryRequest.setGenerateReport(true);
        
        // 设置作物类型
        if (StrUtil.isNotBlank(request.getCropType())) {
            queryRequest.setCropType(request.getCropType());
        }
        
        // 设置报告类型
        if (StrUtil.isNotBlank(request.getReportType())) {
            queryRequest.setReportType(request.getReportType());
        } else {
            queryRequest.setReportType("SUMMARY");
        }
        
        // 设置时间范围
        if (StrUtil.isNotBlank(request.getTimeRange())) {
            queryRequest.setTimeRange(request.getTimeRange());
            setTimeRangeFromString(queryRequest, request.getTimeRange());
        } else if (request.getCustomDateRange() != null && request.getCustomDateRange().length == 2) {
            // 处理自定义时间范围
            queryRequest.setStartTime(request.getCustomDateRange()[0]);
            queryRequest.setEndTime(request.getCustomDateRange()[1]);
            queryRequest.setTimeRange("自定义时间范围");
        }
        
        // 设置记录类型
        if (request.getRecordTypes() != null && !request.getRecordTypes().isEmpty()) {
            queryRequest.setRecordType(String.join(",", request.getRecordTypes()));
        } else {
            queryRequest.setRecordType("IMG"); // 默认图像记录
        }
        
        // 设置分析维度
        if (request.getAnalysisDimensions() != null && !request.getAnalysisDimensions().isEmpty()) {
            queryRequest.setAnalysisDimensions(request.getAnalysisDimensions());
        } else {
            queryRequest.setAnalysisDimensions(Arrays.asList("DISEASE", "CONFIDENCE", "TIME"));
        }
        
        // 设置置信度范围
        if (request.getConfidenceRange() != null && request.getConfidenceRange().length == 2) {
            queryRequest.setMinConfidence(request.getConfidenceRange()[0] / 100.0);
            queryRequest.setMaxConfidence(request.getConfidenceRange()[1] / 100.0);
        }
        
        return queryRequest;
    }
    
    /**
     * 根据时间范围字符串设置具体的开始和结束时间
     */
    private void setTimeRangeFromString(RecordQueryRequest queryRequest, String timeRange) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = null;
        LocalDateTime endTime = now;
        
        switch (timeRange) {
            case "today":
                startTime = now.toLocalDate().atStartOfDay();
                break;
            case "week":
                startTime = now.minusDays(7);
                break;
            case "month":
                startTime = now.minusDays(30);
                break;
            case "quarter":
                startTime = now.minusDays(90);
                break;
            default:
                startTime = now.minusDays(30); // 默认最近30天
                break;
        }
        
        queryRequest.setStartTime(startTime);
        queryRequest.setEndTime(endTime);
    }
    
    /**
     * 报告生成请求DTO
     */
    public static class ReportGenerateRequest {
        private String cropType;
        private String reportType;
        private String timeRange;
        private LocalDateTime[] customDateRange;
        private List<String> recordTypes;
        private List<String> analysisDimensions;
        private Integer[] confidenceRange;
        
        // Getters and Setters
        public String getCropType() { return cropType; }
        public void setCropType(String cropType) { this.cropType = cropType; }
        
        public String getReportType() { return reportType; }
        public void setReportType(String reportType) { this.reportType = reportType; }
        
        public String getTimeRange() { return timeRange; }
        public void setTimeRange(String timeRange) { this.timeRange = timeRange; }
        
        public LocalDateTime[] getCustomDateRange() { return customDateRange; }
        public void setCustomDateRange(LocalDateTime[] customDateRange) { this.customDateRange = customDateRange; }
        
        public List<String> getRecordTypes() { return recordTypes; }
        public void setRecordTypes(List<String> recordTypes) { this.recordTypes = recordTypes; }
        
        public List<String> getAnalysisDimensions() { return analysisDimensions; }
        public void setAnalysisDimensions(List<String> analysisDimensions) { this.analysisDimensions = analysisDimensions; }
        
        public Integer[] getConfidenceRange() { return confidenceRange; }
        public void setConfidenceRange(Integer[] confidenceRange) { this.confidenceRange = confidenceRange; }
        
        @Override
        public String toString() {
            return "ReportGenerateRequest{" +
                    "cropType='" + cropType + '\'' +
                    ", reportType='" + reportType + '\'' +
                    ", timeRange='" + timeRange + '\'' +
                    ", recordTypes=" + recordTypes +
                    ", analysisDimensions=" + analysisDimensions +
                    ", confidenceRange=" + Arrays.toString(confidenceRange) +
                    '}';
        }
    }
}
