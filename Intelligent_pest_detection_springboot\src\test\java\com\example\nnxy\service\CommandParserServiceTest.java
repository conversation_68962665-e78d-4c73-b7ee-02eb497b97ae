package com.example.nnxy.service;

import com.example.nnxy.dto.RecordQueryRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 指令解析服务测试
 */
@SpringBootTest
class CommandParserServiceTest {

    private CommandParserService commandParserService;

    @BeforeEach
    void setUp() {
        commandParserService = new CommandParserService();
    }

    @Test
    void testParseQueryCommand() {
        // 测试查询指令解析
        String command = "查看我的图像检测记录";
        String username = "testuser";
        
        RecordQueryRequest request = commandParserService.parseCommand(command, username);
        
        assertNotNull(request);
        assertEquals("QUERY", request.getOperationType());
        assertEquals("IMG", request.getRecordType());
        assertEquals(username, request.getUsername());
    }

    @Test
    void testParseDeleteCommand() {
        // 测试删除指令解析
        String command = "删除昨天的记录";
        String username = "testuser";
        
        RecordQueryRequest request = commandParserService.parseCommand(command, username);
        
        assertNotNull(request);
        assertEquals("DELETE", request.getOperationType());
        assertEquals(username, request.getUsername());
        assertEquals("昨天", request.getTimeRange());
        assertTrue(request.getNeedConfirmation());
    }

    @Test
    void testParseCountCommand() {
        // 测试统计指令解析
        String command = "统计本周的检测数量";
        String username = "testuser";
        
        RecordQueryRequest request = commandParserService.parseCommand(command, username);
        
        assertNotNull(request);
        assertEquals("COUNT", request.getOperationType());
        assertEquals("本周", request.getTimeRange());
        assertEquals(username, request.getUsername());
    }

    @Test
    void testParseCropType() {
        // 测试作物类型解析
        String command = "搜索玉米相关的记录";
        String username = "testuser";
        
        RecordQueryRequest request = commandParserService.parseCommand(command, username);
        
        assertNotNull(request);
        assertEquals("QUERY", request.getOperationType());
        assertEquals("corn", request.getCropType());
    }

    @Test
    void testParseConfidence() {
        // 测试置信度解析
        String command = "查找置信度大于80%的记录";
        String username = "testuser";
        
        RecordQueryRequest request = commandParserService.parseCommand(command, username);
        
        assertNotNull(request);
        assertEquals("QUERY", request.getOperationType());
        assertEquals(0.8, request.getMinConfidence(), 0.01);
    }

    @Test
    void testIsRecordManagementCommand() {
        // 测试记录管理指令识别
        assertTrue(commandParserService.isRecordManagementCommand("查看我的检测记录"));
        assertTrue(commandParserService.isRecordManagementCommand("删除昨天的记录"));
        assertTrue(commandParserService.isRecordManagementCommand("统计本周的数据"));
        
        assertFalse(commandParserService.isRecordManagementCommand("你好"));
        assertFalse(commandParserService.isRecordManagementCommand("天气怎么样"));
    }

    @Test
    void testIsConfirmCommand() {
        // 测试确认指令识别
        assertTrue(commandParserService.isConfirmCommand("确认删除"));
        assertTrue(commandParserService.isConfirmCommand("是的"));
        assertTrue(commandParserService.isConfirmCommand("确定"));
        
        assertFalse(commandParserService.isCancelCommand("确认删除"));
    }

    @Test
    void testIsCancelCommand() {
        // 测试取消指令识别
        assertTrue(commandParserService.isCancelCommand("取消删除"));
        assertTrue(commandParserService.isCancelCommand("不要"));
        assertTrue(commandParserService.isCancelCommand("算了"));
        
        assertFalse(commandParserService.isConfirmCommand("取消删除"));
    }

    @Test
    void testComplexCommand() {
        // 测试复杂指令解析
        String command = "查看最近7天置信度大于90%的玉米检测记录前10条";
        String username = "testuser";
        
        RecordQueryRequest request = commandParserService.parseCommand(command, username);
        
        assertNotNull(request);
        assertEquals("QUERY", request.getOperationType());
        assertEquals("corn", request.getCropType());
        assertEquals(0.9, request.getMinConfidence(), 0.01);
        assertEquals(10, request.getLimit());
        assertEquals("最近7天", request.getTimeRange());
    }

    @Test
    void testToBuilderMethod() {
        // 测试 toBuilder 方法
        RecordQueryRequest original = RecordQueryRequest.builder()
            .operationType("QUERY")
            .recordType("IMG")
            .username("testuser")
            .build();
        
        RecordQueryRequest modified = original.toBuilder()
            .recordType("VIDEO")
            .build();
        
        assertNotNull(modified);
        assertEquals("QUERY", modified.getOperationType());
        assertEquals("VIDEO", modified.getRecordType());
        assertEquals("testuser", modified.getUsername());
        
        // 原对象不应该被修改
        assertEquals("IMG", original.getRecordType());
    }
}
