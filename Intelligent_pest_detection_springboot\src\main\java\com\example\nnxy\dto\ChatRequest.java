package com.example.nnxy.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 聊天请求DTO
 * <AUTHOR>
 */
@Data
public class ChatRequest {

    /**
     * 用户消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String message;

    /**
     * 用户名
     */
    private String username;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 是否开启流式响应
     */
    private Boolean stream = false;

    /**
     * 温度参数，控制回复的随机性
     */
    private Double temperature = 0.6;

    /**
     * 是否需要执行记录管理操作
     */
    private Boolean needRecordOperation = false;

    /**
     * 记录操作类型
     */
    private String operationType;
}
