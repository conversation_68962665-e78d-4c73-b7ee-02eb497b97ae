package com.example.nnxy.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 记录操作结果DTO
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecordOperationResult {
    
    /**
     * 操作是否成功
     */
    private Boolean success;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 记录类型
     */
    private String recordType;
    
    /**
     * 操作消息
     */
    private String message;
    
    /**
     * 错误信息
     */
    private String error;
    
    /**
     * 查询结果数据
     */
    private Object data;
    
    /**
     * 总记录数
     */
    private Long totalCount;
    
    /**
     * 当前页记录数
     */
    private Integer currentPageCount;
    
    /**
     * 总页数
     */
    private Integer totalPages;
    
    /**
     * 当前页码
     */
    private Integer currentPage;
    
    /**
     * 操作影响的记录数量
     */
    private Integer affectedCount;
    
    /**
     * 统计信息
     */
    private Map<String, Object> statistics;
    
    /**
     * 操作详情列表
     */
    private List<String> details;
    
    /**
     * 是否需要用户确认
     */
    private Boolean needConfirmation;
    
    /**
     * 确认消息
     */
    private String confirmationMessage;
    
    /**
     * 操作建议
     */
    private List<String> suggestions;
    
    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;
    
    /**
     * 创建成功结果
     */
    public static RecordOperationResult success(String operationType, String recordType, String message) {
        return RecordOperationResult.builder()
                .success(true)
                .operationType(operationType)
                .recordType(recordType)
                .message(message)
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static RecordOperationResult error(String operationType, String recordType, String error) {
        return RecordOperationResult.builder()
                .success(false)
                .operationType(operationType)
                .recordType(recordType)
                .error(error)
                .build();
    }
    
    /**
     * 创建需要确认的结果
     */
    public static RecordOperationResult needConfirmation(String operationType, String recordType, 
                                                        String confirmationMessage, Integer affectedCount) {
        return RecordOperationResult.builder()
                .success(false)
                .operationType(operationType)
                .recordType(recordType)
                .needConfirmation(true)
                .confirmationMessage(confirmationMessage)
                .affectedCount(affectedCount)
                .build();
    }
}
