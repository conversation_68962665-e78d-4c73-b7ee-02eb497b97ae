package com.example.nnxy.controller;

import com.example.nnxy.common.Result;
import com.example.nnxy.dto.ChatRequest;
import com.example.nnxy.dto.ChatResponse;
import com.example.nnxy.dto.RecordOperationResult;
import com.example.nnxy.dto.RecordQueryRequest;
import com.example.nnxy.entity.ChatMessage;
import com.example.nnxy.service.ChatService;
import com.example.nnxy.service.CommandParserService;
import com.example.nnxy.service.RecordManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * 聊天控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/chat")
@CrossOrigin(origins = "*")
public class ChatController {

    @Autowired
    private ChatService chatService;

    @Autowired
    private CommandParserService commandParserService;

    @Autowired
    private RecordManagementService recordManagementService;
    
    /**
     * 发送聊天消息
     */
    @PostMapping("/send")
    public Result<ChatResponse> sendMessage(@Valid @RequestBody ChatRequest request) {
        try {
            log.info("收到聊天请求: {}", request.getMessage());
            
            ChatResponse response = chatService.sendMessage(request);
            
            if (response.getSuccess()) {
                return Result.success(response);
            } else {
                return Result.error("-1", response.getError());
            }
            
        } catch (Exception e) {
            log.error("聊天接口异常", e);
            return Result.error("-1", "聊天服务异常，请稍后重试");
        }
    }
    
    /**
     * 获取会话历史
     */
    @GetMapping("/history/{sessionId}")
    public Result<List<ChatMessage>> getHistory(@PathVariable String sessionId) {
        try {
            List<ChatMessage> history = chatService.getSessionHistory(sessionId);
            return Result.success(history);
        } catch (Exception e) {
            log.error("获取聊天历史异常", e);
            return Result.error("-1", "获取聊天历史失败");
        }
    }
    
    /**
     * 清除会话历史
     */
    @DeleteMapping("/history/{sessionId}")
    public Result<String> clearHistory(@PathVariable String sessionId) {
        try {
            chatService.clearSessionHistory(sessionId);
            return Result.success("聊天历史已清除");
        } catch (Exception e) {
            log.error("清除聊天历史异常", e);
            return Result.error("-1", "清除聊天历史失败");
        }
    }
    
    /**
     * 获取所有会话ID
     */
    @GetMapping("/sessions")
    public Result<Set<String>> getAllSessions() {
        try {
            Set<String> sessions = chatService.getAllSessionIds();
            return Result.success(sessions);
        } catch (Exception e) {
            log.error("获取会话列表异常", e);
            return Result.error("-1", "获取会话列表失败");
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("聊天服务正常运行");
    }

    /**
     * 流式聊天接口
     */
    @PostMapping("/stream")
    public SseEmitter streamChat(@Valid @RequestBody ChatRequest request) {
        log.info("收到流式聊天请求: {}", request.getMessage());

        SseEmitter emitter = new SseEmitter(30000L); // 30秒超时

        // 异步处理
        new Thread(() -> {
            try {
                chatService.sendStreamMessage(request, emitter);
            } catch (Exception e) {
                log.error("流式聊天异常", e);
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("聊天服务异常: " + e.getMessage()));
                    emitter.complete();
                } catch (Exception ex) {
                    emitter.completeWithError(ex);
                }
            }
        }).start();

        return emitter;
    }

    /**
     * 测试API连接
     */
    @GetMapping("/test")
    public Result<String> testApi() {
        try {
            boolean connected = chatService.testApiConnection();
            if (connected) {
                return Result.success("API连接测试成功");
            } else {
                return Result.error("-1", "API连接测试失败");
            }
        } catch (Exception e) {
            log.error("API测试异常", e);
            return Result.error("-1", "API测试异常: " + e.getMessage());
        }
    }

    /**
     * 解析记录管理指令
     */
    @PostMapping("/parse-command")
    public Result<RecordQueryRequest> parseCommand(@RequestBody ChatRequest request) {
        try {
            log.info("解析记录管理指令: {}", request.getMessage());

            if (!commandParserService.isRecordManagementCommand(request.getMessage())) {
                return Result.error("-1", "这不是一个记录管理指令");
            }

            RecordQueryRequest queryRequest = commandParserService.parseCommand(
                request.getMessage(), request.getUsername());

            return Result.success(queryRequest);

        } catch (Exception e) {
            log.error("解析指令异常", e);
            return Result.error("-1", "指令解析失败: " + e.getMessage());
        }
    }

    /**
     * 直接执行记录管理操作
     */
    @PostMapping("/execute-record-operation")
    public Result<RecordOperationResult> executeRecordOperation(@RequestBody RecordQueryRequest request) {
        try {
            log.info("执行记录管理操作: {}", request);

            RecordOperationResult result = recordManagementService.executeOperation(request);

            if (result.getSuccess()) {
                return Result.success(result);
            } else {
                return Result.error("-1", result.getError());
            }

        } catch (Exception e) {
            log.error("执行记录操作异常", e);
            return Result.error("-1", "操作执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取记录管理操作的建议指令
     */
    @GetMapping("/record-suggestions")
    public Result<List<String>> getRecordSuggestions() {
        try {
            List<String> suggestions = List.of(
                "查看我的图像检测记录",
                "搜索玉米相关的记录",
                "显示今天的检测记录",
                "统计本周的检测数量",
                "查找置信度大于80%的记录",
                "删除昨天的记录",
                "清除置信度低于50%的记录",
                "统计本月的图像检测记录",
                "查看最近10条检测记录",
                "搜索草莓病害检测记录"
            );

            return Result.success(suggestions);

        } catch (Exception e) {
            log.error("获取建议指令异常", e);
            return Result.error("-1", "获取建议失败");
        }
    }

    /**
     * 检查指令是否为记录管理类型
     */
    @PostMapping("/check-record-command")
    public Result<Boolean> checkRecordCommand(@RequestBody ChatRequest request) {
        try {
            boolean isRecordCommand = commandParserService.isRecordManagementCommand(request.getMessage());
            return Result.success(isRecordCommand);
        } catch (Exception e) {
            log.error("检查指令类型异常", e);
            return Result.error("-1", "检查失败");
        }
    }
}
