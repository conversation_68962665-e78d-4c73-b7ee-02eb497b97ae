package com.example.nnxy.service;

import com.example.nnxy.dto.AnalysisReportDTO;
import com.example.nnxy.dto.RecordQueryRequest;
import com.example.nnxy.entity.ImgRecords;
import com.example.nnxy.entity.VideoRecords;
import com.example.nnxy.entity.CameraRecords;
import com.example.nnxy.mapper.ImgRecordsMapper;
import com.example.nnxy.mapper.VideoRecordsMapper;
import com.example.nnxy.mapper.CameraRecordsMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告分析服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReportAnalysisService {
    
    @Resource
    private ImgRecordsMapper imgRecordsMapper;

    @Resource
    private VideoRecordsMapper videoRecordsMapper;

    @Resource
    private CameraRecordsMapper cameraRecordsMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 作物病害知识库
    private static final Map<String, Map<String, String>> CROP_DISEASE_INFO = new HashMap<>();
    
    static {
        // 玉米病害信息
        Map<String, String> cornDiseases = new HashMap<>();
        cornDiseases.put("blight（疫病）", "玉米疫病是一种严重的真菌性病害，主要危害叶片和茎秆，高温高湿条件下易发生。");
        cornDiseases.put("common_rust（普通锈病）", "玉米普通锈病主要危害叶片，形成锈色孢子堆，影响光合作用。");
        cornDiseases.put("gray_spot（灰斑病）", "玉米灰斑病主要危害叶片，形成灰色斑点，严重时可导致叶片枯死。");
        cornDiseases.put("health（健康）", "植株健康，无明显病害症状。");
        CROP_DISEASE_INFO.put("corn", cornDiseases);
        
        // 水稻病害信息
        Map<String, String> riceDiseases = new HashMap<>();
        riceDiseases.put("Brown_Spot（褐斑病）", "水稻褐斑病主要危害叶片，形成褐色斑点，影响产量。");
        riceDiseases.put("Rice_Blast（稻瘟病）", "稻瘟病是水稻最重要的病害之一，可危害叶片、茎秆和穗部。");
        riceDiseases.put("Bacterial_Blight（细菌性叶枯病）", "水稻细菌性叶枯病主要危害叶片，形成黄色条斑。");
        CROP_DISEASE_INFO.put("rice", riceDiseases);
        
        // 草莓病害信息
        Map<String, String> strawberryDiseases = new HashMap<>();
        strawberryDiseases.put("Angular Leafspot（角斑病）", "草莓角斑病主要危害叶片，形成角状斑点。");
        strawberryDiseases.put("Anthracnose Fruit Rot（炭疽果腐病）", "草莓炭疽果腐病主要危害果实，导致果实腐烂。");
        strawberryDiseases.put("Gray Mold（灰霉病）", "草莓灰霉病是重要的真菌性病害，主要危害果实和叶片。");
        CROP_DISEASE_INFO.put("strawberry", strawberryDiseases);
        
        // 西红柿病害信息
        Map<String, String> tomatoDiseases = new HashMap<>();
        tomatoDiseases.put("Early Blight（早疫病）", "番茄早疫病主要危害叶片和果实，形成同心轮纹状斑点。");
        tomatoDiseases.put("Late Blight（晚疫病）", "番茄晚疫病是毁灭性病害，可快速传播并导致植株死亡。");
        tomatoDiseases.put("Healthy（健康）", "植株健康，无明显病害症状。");
        CROP_DISEASE_INFO.put("tomato", tomatoDiseases);
    }
    
    /**
     * 生成分析报告
     */
    public AnalysisReportDTO generateAnalysisReport(RecordQueryRequest request) {
        log.info("开始生成分析报告，查询条件: {}", request);
        
        String reportId = UUID.randomUUID().toString();
        LocalDateTime generateTime = LocalDateTime.now();
        
        // 查询数据
        List<ImgRecords> imgRecords = queryImgRecords(request);
        List<VideoRecords> videoRecords = queryVideoRecords(request);
        List<CameraRecords> cameraRecords = queryCameraRecords(request);
        
        // 合并所有记录用于分析
        List<DetectionRecord> allRecords = mergeRecords(imgRecords, videoRecords, cameraRecords);
        
        // 生成报告
        AnalysisReportDTO report = AnalysisReportDTO.builder()
                .reportId(reportId)
                .title(generateReportTitle(request))
                .reportType(request.getReportType())
                .generateTime(generateTime)
                .queryConditions(buildQueryConditionsSummary(request))
                .cropType(request.getCropType())
                .timeRange(request.getTimeRange())
                .totalRecords((long) allRecords.size())
                .build();
        
        // 生成各种分析
        report.setExecutiveSummary(generateExecutiveSummary(allRecords, request));
        report.setDiseaseAnalysis(generateDiseaseAnalysis(allRecords, request.getCropType()));
        report.setConfidenceAnalysis(generateConfidenceAnalysis(allRecords));
        report.setTrendAnalysis(generateTrendAnalysis(allRecords));
        report.setRecommendations(generateRecommendations(allRecords, request.getCropType()));
        report.setDetailData(convertToDetailData(allRecords));
        report.setChartData(generateChartData(allRecords));
        
        log.info("分析报告生成完成，报告ID: {}", reportId);
        return report;
    }
    
    /**
     * 查询图像记录
     */
    private List<ImgRecords> queryImgRecords(RecordQueryRequest request) {
        LambdaQueryWrapper<ImgRecords> wrapper = Wrappers.<ImgRecords>lambdaQuery()
                .orderByDesc(ImgRecords::getStartTime);
        
        if (StrUtil.isNotBlank(request.getUsername())) {
            wrapper.eq(ImgRecords::getUsername, request.getUsername());
        }
        if (StrUtil.isNotBlank(request.getCropType())) {
            wrapper.eq(ImgRecords::getKind, request.getCropType());
        }
        if (request.getStartTime() != null) {
            wrapper.ge(ImgRecords::getStartTime, request.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (request.getEndTime() != null) {
            wrapper.le(ImgRecords::getStartTime, request.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        
        return imgRecordsMapper.selectList(wrapper);
    }
    
    /**
     * 查询视频记录
     */
    private List<VideoRecords> queryVideoRecords(RecordQueryRequest request) {
        LambdaQueryWrapper<VideoRecords> wrapper = Wrappers.<VideoRecords>lambdaQuery()
                .orderByDesc(VideoRecords::getStartTime);
        
        if (StrUtil.isNotBlank(request.getUsername())) {
            wrapper.eq(VideoRecords::getUsername, request.getUsername());
        }
        if (StrUtil.isNotBlank(request.getCropType())) {
            wrapper.eq(VideoRecords::getKind, request.getCropType());
        }
        if (request.getStartTime() != null) {
            wrapper.ge(VideoRecords::getStartTime, request.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (request.getEndTime() != null) {
            wrapper.le(VideoRecords::getStartTime, request.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        
        return videoRecordsMapper.selectList(wrapper);
    }
    
    /**
     * 查询摄像头记录
     */
    private List<CameraRecords> queryCameraRecords(RecordQueryRequest request) {
        LambdaQueryWrapper<CameraRecords> wrapper = Wrappers.<CameraRecords>lambdaQuery()
                .orderByDesc(CameraRecords::getStartTime);
        
        if (StrUtil.isNotBlank(request.getUsername())) {
            wrapper.eq(CameraRecords::getUsername, request.getUsername());
        }
        if (StrUtil.isNotBlank(request.getCropType())) {
            wrapper.eq(CameraRecords::getKind, request.getCropType());
        }
        if (request.getStartTime() != null) {
            wrapper.ge(CameraRecords::getStartTime, request.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (request.getEndTime() != null) {
            wrapper.le(CameraRecords::getStartTime, request.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        
        return cameraRecordsMapper.selectList(wrapper);
    }
    
    /**
     * 合并所有类型的记录
     */
    private List<DetectionRecord> mergeRecords(List<ImgRecords> imgRecords,
                                             List<VideoRecords> videoRecords,
                                             List<CameraRecords> cameraRecords) {
        List<DetectionRecord> allRecords = new ArrayList<>();

        // 处理图像记录
        for (ImgRecords record : imgRecords) {
            DetectionRecord detectionRecord = convertImgRecord(record);
            if (detectionRecord != null) {
                allRecords.add(detectionRecord);
            }
        }

        // 处理视频记录（简化处理，实际可能需要更复杂的逻辑）
        for (VideoRecords record : videoRecords) {
            DetectionRecord detectionRecord = DetectionRecord.builder()
                    .id(record.getId().toString())
                    .username(record.getUsername())
                    .kind(record.getKind())
                    .startTime(record.getStartTime())
                    .recordType("VIDEO")
                    .diseases(Arrays.asList("视频检测"))
                    .confidences(Arrays.asList(0.0))
                    .avgConfidence(0.0)
                    .build();
            allRecords.add(detectionRecord);
        }

        // 处理摄像头记录（简化处理）
        for (CameraRecords record : cameraRecords) {
            DetectionRecord detectionRecord = DetectionRecord.builder()
                    .id(record.getId().toString())
                    .username(record.getUsername())
                    .kind(record.getKind())
                    .startTime(record.getStartTime())
                    .recordType("CAMERA")
                    .diseases(Arrays.asList("实时检测"))
                    .confidences(Arrays.asList(0.0))
                    .avgConfidence(0.0)
                    .build();
            allRecords.add(detectionRecord);
        }

        return allRecords;
    }

    /**
     * 转换图像记录
     */
    private DetectionRecord convertImgRecord(ImgRecords record) {
        try {
            List<String> diseases = new ArrayList<>();
            List<Double> confidences = new ArrayList<>();

            if (StrUtil.isNotBlank(record.getLabel())) {
                List<String> labels = objectMapper.readValue(record.getLabel(), new TypeReference<List<String>>() {});
                diseases.addAll(labels);
            }

            if (StrUtil.isNotBlank(record.getConfidence())) {
                List<String> confStrings = objectMapper.readValue(record.getConfidence(), new TypeReference<List<String>>() {});
                for (String confStr : confStrings) {
                    try {
                        double conf = Double.parseDouble(confStr.replace("%", ""));
                        confidences.add(conf);
                    } catch (NumberFormatException e) {
                        confidences.add(0.0);
                    }
                }
            }

            double avgConfidence = confidences.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

            return DetectionRecord.builder()
                    .id(record.getId().toString())
                    .username(record.getUsername())
                    .kind(record.getKind())
                    .startTime(record.getStartTime())
                    .recordType("IMG")
                    .diseases(diseases)
                    .confidences(confidences)
                    .avgConfidence(avgConfidence)
                    .build();
        } catch (Exception e) {
            log.warn("转换图像记录失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 生成报告标题
     */
    private String generateReportTitle(RecordQueryRequest request) {
        StringBuilder title = new StringBuilder();

        if (StrUtil.isNotBlank(request.getCropType())) {
            String cropName = getCropDisplayName(request.getCropType());
            title.append(cropName).append("作物");
        } else {
            title.append("综合作物");
        }

        title.append("检测分析报告");

        if (StrUtil.isNotBlank(request.getTimeRange())) {
            title.append("（").append(request.getTimeRange()).append("）");
        }

        return title.toString();
    }

    /**
     * 获取作物显示名称
     */
    private String getCropDisplayName(String cropType) {
        switch (cropType) {
            case "corn": return "玉米";
            case "rice": return "水稻";
            case "strawberry": return "草莓";
            case "tomato": return "西红柿";
            default: return cropType;
        }
    }

    /**
     * 构建查询条件摘要
     */
    private String buildQueryConditionsSummary(RecordQueryRequest request) {
        List<String> conditions = new ArrayList<>();

        if (StrUtil.isNotBlank(request.getCropType())) {
            conditions.add("作物类型: " + getCropDisplayName(request.getCropType()));
        }
        if (StrUtil.isNotBlank(request.getTimeRange())) {
            conditions.add("时间范围: " + request.getTimeRange());
        }
        if (StrUtil.isNotBlank(request.getUsername())) {
            conditions.add("用户: " + request.getUsername());
        }
        if (request.getMinConfidence() != null) {
            conditions.add("最低置信度: " + (request.getMinConfidence() * 100) + "%");
        }

        return conditions.isEmpty() ? "无特定条件" : String.join(", ", conditions);
    }

    /**
     * 生成执行摘要
     */
    private AnalysisReportDTO.ExecutiveSummary generateExecutiveSummary(List<DetectionRecord> records, RecordQueryRequest request) {
        Map<String, Object> keyMetrics = new HashMap<>();
        List<String> highlights = new ArrayList<>();

        keyMetrics.put("totalRecords", records.size());
        keyMetrics.put("cropType", getCropDisplayName(request.getCropType()));

        // 计算平均置信度
        double avgConfidence = records.stream()
                .mapToDouble(DetectionRecord::getAvgConfidence)
                .average().orElse(0.0);
        keyMetrics.put("averageConfidence", Math.round(avgConfidence * 100.0) / 100.0);

        // 统计病害类型数量
        Set<String> uniqueDiseases = records.stream()
                .flatMap(r -> r.getDiseases().stream())
                .collect(Collectors.toSet());
        keyMetrics.put("diseaseTypes", uniqueDiseases.size());

        // 生成亮点
        if (records.size() > 0) {
            highlights.add(String.format("共分析了 %d 条检测记录", records.size()));
            highlights.add(String.format("平均检测置信度为 %.1f%%", avgConfidence));
            highlights.add(String.format("发现 %d 种不同的病害类型", uniqueDiseases.size()));
        }

        String overview = String.format("本报告分析了%s作物的%d条检测记录，平均置信度为%.1f%%，为农业生产提供科学依据。",
                getCropDisplayName(request.getCropType()), records.size(), avgConfidence);

        return AnalysisReportDTO.ExecutiveSummary.builder()
                .overview(overview)
                .keyMetrics(keyMetrics)
                .highlights(highlights)
                .build();
    }

    /**
     * 生成病害分析
     */
    private AnalysisReportDTO.DiseaseAnalysis generateDiseaseAnalysis(List<DetectionRecord> records, String cropType) {
        Map<String, Integer> diseaseDistribution = new HashMap<>();
        Map<String, List<Double>> diseaseConfidences = new HashMap<>();

        // 统计病害分布
        for (DetectionRecord record : records) {
            for (int i = 0; i < record.getDiseases().size(); i++) {
                String disease = record.getDiseases().get(i);
                diseaseDistribution.put(disease, diseaseDistribution.getOrDefault(disease, 0) + 1);

                if (i < record.getConfidences().size()) {
                    diseaseConfidences.computeIfAbsent(disease, k -> new ArrayList<>())
                            .add(record.getConfidences().get(i));
                }
            }
        }

        // 找出最常见的病害
        String mostCommonDisease = diseaseDistribution.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("无");

        // 计算病害率（非健康记录的比例）
        long diseaseCount = diseaseDistribution.entrySet().stream()
                .filter(entry -> !entry.getKey().toLowerCase().contains("health") &&
                               !entry.getKey().toLowerCase().contains("健康"))
                .mapToLong(Map.Entry::getValue)
                .sum();
        double diseaseRate = records.size() > 0 ? (double) diseaseCount / records.size() : 0.0;

        // 生成病害详情
        List<AnalysisReportDTO.DiseaseAnalysis.DiseaseDetail> diseaseDetails = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : diseaseDistribution.entrySet()) {
            String diseaseName = entry.getKey();
            Integer count = entry.getValue();
            double percentage = records.size() > 0 ? (double) count / records.size() * 100 : 0.0;

            double avgConfidence = diseaseConfidences.getOrDefault(diseaseName, Arrays.asList(0.0))
                    .stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

            String severity = getSeverityLevel(avgConfidence, percentage);
            String description = getDiseaseDescription(diseaseName, cropType);

            diseaseDetails.add(AnalysisReportDTO.DiseaseAnalysis.DiseaseDetail.builder()
                    .diseaseName(diseaseName)
                    .count(count)
                    .percentage(Math.round(percentage * 100.0) / 100.0)
                    .avgConfidence(Math.round(avgConfidence * 100.0) / 100.0)
                    .severity(severity)
                    .description(description)
                    .build());
        }

        // 按数量排序
        diseaseDetails.sort((a, b) -> b.getCount().compareTo(a.getCount()));

        return AnalysisReportDTO.DiseaseAnalysis.builder()
                .diseaseDistribution(diseaseDistribution)
                .mostCommonDisease(mostCommonDisease)
                .diseaseRate(Math.round(diseaseRate * 10000.0) / 100.0) // 保留两位小数的百分比
                .diseaseDetails(diseaseDetails)
                .build();
    }

    /**
     * 获取严重程度等级
     */
    private String getSeverityLevel(double avgConfidence, double percentage) {
        if (avgConfidence > 80 && percentage > 20) {
            return "高";
        } else if (avgConfidence > 60 && percentage > 10) {
            return "中";
        } else {
            return "低";
        }
    }

    /**
     * 获取病害描述
     */
    private String getDiseaseDescription(String diseaseName, String cropType) {
        if (StrUtil.isBlank(cropType) || !CROP_DISEASE_INFO.containsKey(cropType)) {
            return "暂无详细描述";
        }

        Map<String, String> diseaseInfo = CROP_DISEASE_INFO.get(cropType);
        return diseaseInfo.getOrDefault(diseaseName, "暂无详细描述");
    }

    /**
     * 生成置信度分析
     */
    private AnalysisReportDTO.ConfidenceAnalysis generateConfidenceAnalysis(List<DetectionRecord> records) {
        List<Double> allConfidences = records.stream()
                .flatMap(r -> r.getConfidences().stream())
                .collect(Collectors.toList());

        if (allConfidences.isEmpty()) {
            return AnalysisReportDTO.ConfidenceAnalysis.builder()
                    .averageConfidence(0.0)
                    .highConfidenceRate(0.0)
                    .mediumConfidenceRate(0.0)
                    .lowConfidenceRate(0.0)
                    .confidenceByDisease(new HashMap<>())
                    .build();
        }

        double averageConfidence = allConfidences.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

        long highCount = allConfidences.stream().mapToLong(c -> c >= 80 ? 1 : 0).sum();
        long mediumCount = allConfidences.stream().mapToLong(c -> c >= 60 && c < 80 ? 1 : 0).sum();
        long lowCount = allConfidences.stream().mapToLong(c -> c < 60 ? 1 : 0).sum();

        double total = allConfidences.size();
        double highRate = total > 0 ? highCount / total * 100 : 0.0;
        double mediumRate = total > 0 ? mediumCount / total * 100 : 0.0;
        double lowRate = total > 0 ? lowCount / total * 100 : 0.0;

        // 按病害类型统计置信度
        Map<String, Double> confidenceByDisease = new HashMap<>();
        Map<String, List<Double>> diseaseConfidences = new HashMap<>();

        for (DetectionRecord record : records) {
            for (int i = 0; i < record.getDiseases().size() && i < record.getConfidences().size(); i++) {
                String disease = record.getDiseases().get(i);
                double confidence = record.getConfidences().get(i);
                diseaseConfidences.computeIfAbsent(disease, k -> new ArrayList<>()).add(confidence);
            }
        }

        for (Map.Entry<String, List<Double>> entry : diseaseConfidences.entrySet()) {
            double avgConf = entry.getValue().stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            confidenceByDisease.put(entry.getKey(), Math.round(avgConf * 100.0) / 100.0);
        }

        return AnalysisReportDTO.ConfidenceAnalysis.builder()
                .averageConfidence(Math.round(averageConfidence * 100.0) / 100.0)
                .highConfidenceRate(Math.round(highRate * 100.0) / 100.0)
                .mediumConfidenceRate(Math.round(mediumRate * 100.0) / 100.0)
                .lowConfidenceRate(Math.round(lowRate * 100.0) / 100.0)
                .confidenceByDisease(confidenceByDisease)
                .build();
    }

    /**
     * 生成趋势分析
     */
    private AnalysisReportDTO.TrendAnalysis generateTrendAnalysis(List<DetectionRecord> records) {
        Map<String, Integer> dailyCount = new HashMap<>();
        Map<String, Map<String, Integer>> dailyDiseaseCount = new HashMap<>();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (DetectionRecord record : records) {
            try {
                LocalDateTime dateTime = LocalDateTime.parse(record.getStartTime(),
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String date = dateTime.format(formatter);

                dailyCount.put(date, dailyCount.getOrDefault(date, 0) + 1);

                Map<String, Integer> diseaseCount = dailyDiseaseCount.computeIfAbsent(date, k -> new HashMap<>());
                for (String disease : record.getDiseases()) {
                    diseaseCount.put(disease, diseaseCount.getOrDefault(disease, 0) + 1);
                }
            } catch (Exception e) {
                log.warn("解析时间失败: {}", record.getStartTime());
            }
        }

        List<AnalysisReportDTO.TrendAnalysis.TimePoint> timePoints = dailyCount.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> AnalysisReportDTO.TrendAnalysis.TimePoint.builder()
                        .date(entry.getKey())
                        .count(entry.getValue())
                        .diseaseCount(dailyDiseaseCount.getOrDefault(entry.getKey(), new HashMap<>()))
                        .build())
                .collect(Collectors.toList());

        String trend = analyzeTrend(timePoints);
        String seasonalPattern = "需要更多数据进行季节性分析";

        return AnalysisReportDTO.TrendAnalysis.builder()
                .timePoints(timePoints)
                .trend(trend)
                .seasonalPattern(seasonalPattern)
                .build();
    }

    /**
     * 分析趋势
     */
    private String analyzeTrend(List<AnalysisReportDTO.TrendAnalysis.TimePoint> timePoints) {
        if (timePoints.size() < 2) {
            return "数据不足，无法分析趋势";
        }

        int firstHalf = timePoints.subList(0, timePoints.size() / 2).stream()
                .mapToInt(AnalysisReportDTO.TrendAnalysis.TimePoint::getCount).sum();
        int secondHalf = timePoints.subList(timePoints.size() / 2, timePoints.size()).stream()
                .mapToInt(AnalysisReportDTO.TrendAnalysis.TimePoint::getCount).sum();

        if (secondHalf > firstHalf * 1.1) {
            return "检测频率呈上升趋势";
        } else if (secondHalf < firstHalf * 0.9) {
            return "检测频率呈下降趋势";
        } else {
            return "检测频率保持稳定";
        }
    }

    /**
     * 生成建议
     */
    private List<String> generateRecommendations(List<DetectionRecord> records, String cropType) {
        List<String> recommendations = new ArrayList<>();

        if (records.isEmpty()) {
            recommendations.add("建议增加检测频率，及时发现潜在病害");
            return recommendations;
        }

        // 基于病害分析的建议
        Map<String, Integer> diseaseCount = new HashMap<>();
        for (DetectionRecord record : records) {
            for (String disease : record.getDiseases()) {
                diseaseCount.put(disease, diseaseCount.getOrDefault(disease, 0) + 1);
            }
        }

        // 找出主要病害
        String mainDisease = diseaseCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("");

        if (StrUtil.isNotBlank(mainDisease) && !mainDisease.toLowerCase().contains("health")) {
            recommendations.add(String.format("主要病害为%s，建议加强针对性防治措施", mainDisease));
        }

        // 基于置信度的建议
        double avgConfidence = records.stream().mapToDouble(DetectionRecord::getAvgConfidence).average().orElse(0.0);
        if (avgConfidence < 70) {
            recommendations.add("检测置信度偏低，建议改善图像质量或调整检测参数");
        }

        // 基于作物类型的建议
        if (StrUtil.isNotBlank(cropType)) {
            recommendations.addAll(getCropSpecificRecommendations(cropType, diseaseCount));
        }

        recommendations.add("建议定期进行检测，建立长期监控体系");
        recommendations.add("结合实地观察，验证AI检测结果的准确性");

        return recommendations;
    }

    /**
     * 获取作物特定建议
     */
    private List<String> getCropSpecificRecommendations(String cropType, Map<String, Integer> diseaseCount) {
        List<String> recommendations = new ArrayList<>();

        switch (cropType) {
            case "corn":
                recommendations.add("玉米生长期注意通风透光，避免高温高湿环境");
                if (diseaseCount.containsKey("blight（疫病）")) {
                    recommendations.add("发现疫病时，及时喷施杀菌剂，清除病残体");
                }
                break;
            case "rice":
                recommendations.add("水稻田间管理要注意水层控制，避免长期深水");
                if (diseaseCount.containsKey("Rice_Blast（稻瘟病）")) {
                    recommendations.add("稻瘟病高发期，建议预防性施药");
                }
                break;
            case "strawberry":
                recommendations.add("草莓栽培注意排水通风，控制湿度");
                if (diseaseCount.containsKey("Gray Mold（灰霉病）")) {
                    recommendations.add("灰霉病防治重点在预防，保持环境干燥");
                }
                break;
            case "tomato":
                recommendations.add("番茄种植注意轮作，避免连作障碍");
                if (diseaseCount.containsKey("Late Blight（晚疫病）")) {
                    recommendations.add("晚疫病传播快，发现后立即隔离并处理");
                }
                break;
        }

        return recommendations;
    }

    /**
     * 转换为详细数据
     */
    private List<Object> convertToDetailData(List<DetectionRecord> records) {
        return records.stream().map(record -> {
            Map<String, Object> data = new HashMap<>();
            data.put("id", record.getId());
            data.put("username", record.getUsername());
            data.put("cropType", getCropDisplayName(record.getKind()));
            data.put("recordType", record.getRecordType());
            data.put("startTime", record.getStartTime());
            data.put("diseases", record.getDiseases());
            data.put("confidences", record.getConfidences());
            data.put("avgConfidence", record.getAvgConfidence());
            return data;
        }).collect(Collectors.toList());
    }

    /**
     * 生成图表数据
     */
    private Map<String, Object> generateChartData(List<DetectionRecord> records) {
        Map<String, Object> chartData = new HashMap<>();

        // 病害分布饼图数据
        Map<String, Integer> diseaseDistribution = new HashMap<>();
        for (DetectionRecord record : records) {
            for (String disease : record.getDiseases()) {
                diseaseDistribution.put(disease, diseaseDistribution.getOrDefault(disease, 0) + 1);
            }
        }
        chartData.put("diseaseDistribution", diseaseDistribution);

        // 置信度分布柱状图数据
        Map<String, Integer> confidenceDistribution = new HashMap<>();
        confidenceDistribution.put("高置信度(≥80%)", 0);
        confidenceDistribution.put("中置信度(60-80%)", 0);
        confidenceDistribution.put("低置信度(<60%)", 0);

        for (DetectionRecord record : records) {
            for (Double confidence : record.getConfidences()) {
                if (confidence >= 80) {
                    confidenceDistribution.put("高置信度(≥80%)", confidenceDistribution.get("高置信度(≥80%)") + 1);
                } else if (confidence >= 60) {
                    confidenceDistribution.put("中置信度(60-80%)", confidenceDistribution.get("中置信度(60-80%)") + 1);
                } else {
                    confidenceDistribution.put("低置信度(<60%)", confidenceDistribution.get("低置信度(<60%)") + 1);
                }
            }
        }
        chartData.put("confidenceDistribution", confidenceDistribution);

        // 时间趋势线图数据
        Map<String, Integer> dailyCount = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (DetectionRecord record : records) {
            try {
                LocalDateTime dateTime = LocalDateTime.parse(record.getStartTime(),
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String date = dateTime.format(formatter);
                dailyCount.put(date, dailyCount.getOrDefault(date, 0) + 1);
            } catch (Exception e) {
                // 忽略解析错误
            }
        }
        chartData.put("dailyTrend", dailyCount);

        return chartData;
    }

    /**
     * 检测记录统一数据结构
     */
    @lombok.Data
    @lombok.Builder
    @lombok.AllArgsConstructor
    @lombok.NoArgsConstructor
    public static class DetectionRecord {
        private String id;
        private String username;
        private String kind;
        private String startTime;
        private String recordType; // IMG, VIDEO, CAMERA
        private List<String> diseases;
        private List<Double> confidences;
        private Double avgConfidence;
    }
}
