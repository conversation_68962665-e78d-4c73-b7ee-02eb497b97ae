package com.example.nnxy.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.nnxy.dto.AnalysisReportDTO;
import com.example.nnxy.dto.RecordOperationResult;
import com.example.nnxy.dto.RecordQueryRequest;
import com.example.nnxy.entity.CameraRecords;
import com.example.nnxy.entity.ImgRecords;
import com.example.nnxy.entity.VideoRecords;
import com.example.nnxy.mapper.CameraRecordsMapper;
import com.example.nnxy.mapper.ImgRecordsMapper;
import com.example.nnxy.mapper.VideoRecordsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 记录管理服务
 * 提供图像、视频、摄像头检测记录的智能查询、删除等操作
 * <AUTHOR>
 */
@Slf4j
@Service
public class RecordManagementService {

    @Resource
    private ImgRecordsMapper imgRecordsMapper;

    @Resource
    private VideoRecordsMapper videoRecordsMapper;

    @Resource
    private CameraRecordsMapper cameraRecordsMapper;

    @Resource
    private ReportAnalysisService reportAnalysisService;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 执行记录操作
     */
    public RecordOperationResult executeOperation(RecordQueryRequest request) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("执行记录操作: {}", request);

            RecordOperationResult result;

            switch (request.getOperationType()) {
                case "QUERY":
                    result = executeQuery(request);
                    break;
                case "DELETE":
                    result = executeDelete(request);
                    break;
                case "COUNT":
                    result = executeCount(request);
                    break;
                case "REPORT":
                    result = executeReport(request);
                    break;
                default:
                    result = RecordOperationResult.error(
                            request.getOperationType(),
                            request.getRecordType(),
                            "不支持的操作类型: " + request.getOperationType()
                    );
            }

            result.setExecutionTime(System.currentTimeMillis() - startTime);
            return result;

        } catch (Exception e) {
            log.error("执行记录操作失败", e);
            return RecordOperationResult.error(
                    request.getOperationType(),
                    request.getRecordType(),
                    "操作执行失败: " + e.getMessage()
            );
        }
    }

    /**
     * 执行查询操作
     */
    private RecordOperationResult executeQuery(RecordQueryRequest request) {
        if (request.getRecordType() != null) {
            // 查询特定类型的记录
            return querySpecificType(request);
        } else {
            // 查询所有类型的记录
            return queryAllTypes(request);
        }
    }

    /**
     * 查询特定类型的记录
     */
    private RecordOperationResult querySpecificType(RecordQueryRequest request) {
        switch (request.getRecordType()) {
            case "IMG":
                return queryImgRecords(request);
            case "VIDEO":
                return queryVideoRecords(request);
            case "CAMERA":
                return queryCameraRecords(request);
            default:
                return RecordOperationResult.error(
                        request.getOperationType(),
                        request.getRecordType(),
                        "不支持的记录类型: " + request.getRecordType()
                );
        }
    }

    /**
     * 查询图像记录
     */
    private RecordOperationResult queryImgRecords(RecordQueryRequest request) {
        log.info("开始查询图像记录，用户: {}", request.getUsername());
        LambdaQueryWrapper<ImgRecords> wrapper = buildImgQueryWrapper(request);

        // 调试：先查询总数
        long totalCount = imgRecordsMapper.selectCount(wrapper);
        log.info("图像记录查询条件匹配的总数: {}", totalCount);

        if (request.getLimit() != null && request.getLimit() > 0) {
            // 限制查询数量
            wrapper.last("LIMIT " + request.getLimit());
            List<ImgRecords> records = imgRecordsMapper.selectList(wrapper);

            return RecordOperationResult.builder()
                    .success(true)
                    .operationType(request.getOperationType())
                    .recordType(request.getRecordType())
                    .message(String.format("查询到 %d 条图像检测记录", records.size()))
                    .data(records)
                    .currentPageCount(records.size())
                    .totalCount((long) records.size())
                    .needConfirmation(false)
                    .build();
        } else {
            // 分页查询
            int pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
            Page<ImgRecords> page = new Page<>(pageNum, pageSize);
            Page<ImgRecords> result = imgRecordsMapper.selectPage(page, wrapper);

            return RecordOperationResult.builder()
                    .success(true)
                    .operationType(request.getOperationType())
                    .recordType(request.getRecordType())
                    .message(String.format("查询到 %d 条图像检测记录（第%d页，共%d页）",
                            result.getRecords().size(), result.getCurrent(), result.getPages()))
                    .data(result.getRecords())
                    .totalCount(result.getTotal())
                    .currentPageCount((int) result.getSize())
                    .totalPages((int) result.getPages())
                    .currentPage((int) result.getCurrent())
                    .needConfirmation(false)
                    .build();
        }
    }

    /**
     * 查询视频记录
     */
    private RecordOperationResult queryVideoRecords(RecordQueryRequest request) {
        LambdaQueryWrapper<VideoRecords> wrapper = buildVideoQueryWrapper(request);

        if (request.getLimit() != null && request.getLimit() > 0) {
            wrapper.last("LIMIT " + request.getLimit());
            List<VideoRecords> records = videoRecordsMapper.selectList(wrapper);

            return RecordOperationResult.builder()
                    .success(true)
                    .operationType(request.getOperationType())
                    .recordType(request.getRecordType())
                    .message(String.format("查询到 %d 条视频检测记录", records.size()))
                    .data(records)
                    .currentPageCount(records.size())
                    .totalCount((long) records.size())
                    .needConfirmation(false)
                    .build();
        } else {
            int pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
            Page<VideoRecords> page = new Page<>(pageNum, pageSize);
            Page<VideoRecords> result = videoRecordsMapper.selectPage(page, wrapper);

            return RecordOperationResult.builder()
                    .success(true)
                    .operationType(request.getOperationType())
                    .recordType(request.getRecordType())
                    .message(String.format("查询到 %d 条视频检测记录（第%d页，共%d页）",
                            result.getRecords().size(), result.getCurrent(), result.getPages()))
                    .data(result.getRecords())
                    .totalCount(result.getTotal())
                    .currentPageCount((int) result.getSize())
                    .totalPages((int) result.getPages())
                    .currentPage((int) result.getCurrent())
                    .needConfirmation(false)
                    .build();
        }
    }

    /**
     * 查询摄像头记录
     */
    private RecordOperationResult queryCameraRecords(RecordQueryRequest request) {
        LambdaQueryWrapper<CameraRecords> wrapper = buildCameraQueryWrapper(request);

        if (request.getLimit() != null && request.getLimit() > 0) {
            wrapper.last("LIMIT " + request.getLimit());
            List<CameraRecords> records = cameraRecordsMapper.selectList(wrapper);

            return RecordOperationResult.builder()
                    .success(true)
                    .operationType(request.getOperationType())
                    .recordType(request.getRecordType())
                    .message(String.format("查询到 %d 条摄像头检测记录", records.size()))
                    .data(records)
                    .currentPageCount(records.size())
                    .totalCount((long) records.size())
                    .needConfirmation(false)
                    .build();
        } else {
            int pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
            Page<CameraRecords> page = new Page<>(pageNum, pageSize);
            Page<CameraRecords> result = cameraRecordsMapper.selectPage(page, wrapper);

            return RecordOperationResult.builder()
                    .success(true)
                    .operationType(request.getOperationType())
                    .recordType(request.getRecordType())
                    .message(String.format("查询到 %d 条摄像头检测记录（第%d页，共%d页）",
                            result.getRecords().size(), result.getCurrent(), result.getPages()))
                    .data(result.getRecords())
                    .totalCount(result.getTotal())
                    .currentPageCount((int) result.getSize())
                    .totalPages((int) result.getPages())
                    .currentPage((int) result.getCurrent())
                    .needConfirmation(false)
                    .build();
        }
    }

    /**
     * 查询所有类型的记录
     */
    private RecordOperationResult queryAllTypes(RecordQueryRequest request) {
        Map<String, Object> allRecords = new HashMap<>();
        Map<String, Object> statistics = new HashMap<>();

        // 查询图像记录
        LambdaQueryWrapper<ImgRecords> imgWrapper = buildImgQueryWrapper(request);
        List<ImgRecords> imgRecords = imgRecordsMapper.selectList(imgWrapper);
        allRecords.put("imageRecords", imgRecords);
        statistics.put("imageCount", imgRecords.size());

        // 查询视频记录
        LambdaQueryWrapper<VideoRecords> videoWrapper = buildVideoQueryWrapper(request);
        List<VideoRecords> videoRecords = videoRecordsMapper.selectList(videoWrapper);
        allRecords.put("videoRecords", videoRecords);
        statistics.put("videoCount", videoRecords.size());

        // 查询摄像头记录
        LambdaQueryWrapper<CameraRecords> cameraWrapper = buildCameraQueryWrapper(request);
        List<CameraRecords> cameraRecords = cameraRecordsMapper.selectList(cameraWrapper);
        allRecords.put("cameraRecords", cameraRecords);
        statistics.put("cameraCount", cameraRecords.size());

        int totalCount = imgRecords.size() + videoRecords.size() + cameraRecords.size();
        statistics.put("totalCount", totalCount);

        return RecordOperationResult.builder()
                .success(true)
                .operationType(request.getOperationType())
                .recordType("ALL")
                .message(String.format("查询到总计 %d 条检测记录（图像:%d，视频:%d，摄像头:%d）",
                        totalCount, imgRecords.size(), videoRecords.size(), cameraRecords.size()))
                .data(allRecords)
                .totalCount((long) totalCount)
                .statistics(statistics)
                .needConfirmation(false)
                .build();
    }

    /**
     * 执行报告生成操作
     */
    private RecordOperationResult executeReport(RecordQueryRequest request) {
        try {
            log.info("开始生成分析报告，请求参数: {}", request);

            // 生成分析报告
            AnalysisReportDTO report = reportAnalysisService.generateAnalysisReport(request);

            return RecordOperationResult.builder()
                    .success(true)
                    .operationType(request.getOperationType())
                    .recordType(request.getRecordType())
                    .message(String.format("成功生成%s分析报告，共分析%d条记录",
                            report.getTitle(), report.getTotalRecords()))
                    .data(report)
                    .totalCount(report.getTotalRecords())
                    .needConfirmation(false)
                    .build();

        } catch (Exception e) {
            log.error("生成分析报告失败", e);
            return RecordOperationResult.error(
                    request.getOperationType(),
                    request.getRecordType(),
                    "生成分析报告失败: " + e.getMessage()
            );
        }
    }

    /**
     * 执行统计操作
     */
    private RecordOperationResult executeCount(RecordQueryRequest request) {
        Map<String, Object> statistics = new HashMap<>();

        if (request.getRecordType() != null) {
            // 统计特定类型
            long count = countSpecificType(request);
            statistics.put(request.getRecordType().toLowerCase() + "Count", count);

            return RecordOperationResult.builder()
                    .success(true)
                    .operationType(request.getOperationType())
                    .recordType(request.getRecordType())
                    .message(String.format("找到 %d 条%s检测记录", count, getRecordTypeName(request.getRecordType())))
                    .totalCount(count)
                    .statistics(statistics)
                    .needConfirmation(false)
                    .build();
        } else {
            // 统计所有类型
            long imgCount = countSpecificType(request.toBuilder().recordType("IMG").build());
            long videoCount = countSpecificType(request.toBuilder().recordType("VIDEO").build());
            long cameraCount = countSpecificType(request.toBuilder().recordType("CAMERA").build());
            long totalCount = imgCount + videoCount + cameraCount;

            statistics.put("imageCount", imgCount);
            statistics.put("videoCount", videoCount);
            statistics.put("cameraCount", cameraCount);
            statistics.put("totalCount", totalCount);

            return RecordOperationResult.builder()
                    .success(true)
                    .operationType(request.getOperationType())
                    .recordType("ALL")
                    .message(String.format("总计 %d 条检测记录（图像:%d，视频:%d，摄像头:%d）",
                            totalCount, imgCount, videoCount, cameraCount))
                    .totalCount(totalCount)
                    .statistics(statistics)
                    .needConfirmation(false)
                    .build();
        }
    }

    /**
     * 统计特定类型的记录数量
     */
    private long countSpecificType(RecordQueryRequest request) {
        switch (request.getRecordType()) {
            case "IMG":
                return imgRecordsMapper.selectCount(buildImgQueryWrapper(request));
            case "VIDEO":
                return videoRecordsMapper.selectCount(buildVideoQueryWrapper(request));
            case "CAMERA":
                return cameraRecordsMapper.selectCount(buildCameraQueryWrapper(request));
            default:
                return 0;
        }
    }

    /**
     * 执行删除操作
     */
    @Transactional
    public RecordOperationResult executeDelete(RecordQueryRequest request) {
        // 如果需要确认且没有明确的记录ID，先返回确认信息
        if (request.getNeedConfirmation() && (request.getRecordIds() == null || request.getRecordIds().isEmpty())) {
            return getDeleteConfirmation(request);
        }

        int deletedCount = 0;
        List<String> details = new ArrayList<>();

        if (request.getRecordIds() != null && !request.getRecordIds().isEmpty()) {
            // 按ID删除
            deletedCount = deleteByIds(request);
            details.add("按指定ID删除记录");
        } else {
            // 按条件删除
            deletedCount = deleteByConditions(request);
            details.add("按查询条件删除记录");
        }

        return RecordOperationResult.builder()
                .success(true)
                .operationType(request.getOperationType())
                .recordType(request.getRecordType())
                .message(String.format("成功删除 %d 条%s检测记录", deletedCount, getRecordTypeName(request.getRecordType())))
                .affectedCount(deletedCount)
                .details(details)
                .needConfirmation(false)
                .build();
    }

    /**
     * 获取删除确认信息
     */
    private RecordOperationResult getDeleteConfirmation(RecordQueryRequest request) {
        long count = 0;
        String recordTypeName = "所有";

        if (request.getRecordType() != null) {
            count = countSpecificType(request);
            recordTypeName = getRecordTypeName(request.getRecordType());
        } else {
            // 统计所有类型
            count += countSpecificType(request.toBuilder().recordType("IMG").build());
            count += countSpecificType(request.toBuilder().recordType("VIDEO").build());
            count += countSpecificType(request.toBuilder().recordType("CAMERA").build());
        }

        String confirmationMessage = String.format(
                "确认要删除 %d 条%s检测记录吗？此操作不可撤销！",
                count, recordTypeName
        );

        if (request.getTimeRange() != null) {
            confirmationMessage += String.format("（时间范围：%s）", request.getTimeRange());
        }

        return RecordOperationResult.needConfirmation(
                request.getOperationType(),
                request.getRecordType(),
                confirmationMessage,
                (int) count
        );
    }

    /**
     * 按ID删除记录
     */
    private int deleteByIds(RecordQueryRequest request) {
        int deletedCount = 0;

        if (request.getRecordType() == null || "IMG".equals(request.getRecordType())) {
            deletedCount += imgRecordsMapper.deleteBatchIds(request.getRecordIds());
        }
        if (request.getRecordType() == null || "VIDEO".equals(request.getRecordType())) {
            deletedCount += videoRecordsMapper.deleteBatchIds(request.getRecordIds());
        }
        if (request.getRecordType() == null || "CAMERA".equals(request.getRecordType())) {
            deletedCount += cameraRecordsMapper.deleteBatchIds(request.getRecordIds());
        }

        return deletedCount;
    }

    /**
     * 按条件删除记录
     */
    private int deleteByConditions(RecordQueryRequest request) {
        int deletedCount = 0;

        if (request.getRecordType() == null || "IMG".equals(request.getRecordType())) {
            deletedCount += imgRecordsMapper.delete(buildImgQueryWrapper(request));
        }
        if (request.getRecordType() == null || "VIDEO".equals(request.getRecordType())) {
            deletedCount += videoRecordsMapper.delete(buildVideoQueryWrapper(request));
        }
        if (request.getRecordType() == null || "CAMERA".equals(request.getRecordType())) {
            deletedCount += cameraRecordsMapper.delete(buildCameraQueryWrapper(request));
        }

        return deletedCount;
    }

    /**
     * 构建图像记录查询条件
     */
    private LambdaQueryWrapper<ImgRecords> buildImgQueryWrapper(RecordQueryRequest request) {
        log.info("构建图像记录查询条件，请求参数: {}", request);
        LambdaQueryWrapper<ImgRecords> wrapper = Wrappers.<ImgRecords>lambdaQuery();

        // 排序
        if ("ASC".equals(request.getSortDirection())) {
            wrapper.orderByAsc(ImgRecords::getStartTime);
        } else {
            wrapper.orderByDesc(ImgRecords::getStartTime);
        }

        // 用户名条件
        if (StrUtil.isNotBlank(request.getUsername())) {
            wrapper.eq(ImgRecords::getUsername, request.getUsername());
        }

        // 作物类型条件
        if (StrUtil.isNotBlank(request.getCropType())) {
            wrapper.eq(ImgRecords::getKind, request.getCropType());
        }

        // 标签条件
        if (StrUtil.isNotBlank(request.getLabel())) {
            wrapper.like(ImgRecords::getLabel, request.getLabel());
        }

        // 置信度条件
        if (request.getMinConfidence() != null) {
            wrapper.ge(ImgRecords::getConf, request.getMinConfidence().toString());
        }
        if (request.getMaxConfidence() != null) {
            wrapper.le(ImgRecords::getConf, request.getMaxConfidence().toString());
        }

        // 时间范围条件
        if (request.getStartTime() != null) {
            wrapper.ge(ImgRecords::getStartTime, request.getStartTime().format(DATE_FORMATTER));
        }
        if (request.getEndTime() != null) {
            wrapper.le(ImgRecords::getStartTime, request.getEndTime().format(DATE_FORMATTER));
        }

        // 模型文件条件
        if (StrUtil.isNotBlank(request.getModelWeight())) {
            wrapper.eq(ImgRecords::getWeight, request.getModelWeight());
        }

        // 关键词搜索 - 只对有效关键词进行搜索
        if (StrUtil.isNotBlank(request.getKeyword()) && !isBasicQuery(request)) {
            log.info("添加关键词搜索条件: {}", request.getKeyword());
            // 在label字段中搜索（病害名称）
            wrapper.and(w -> w.like(ImgRecords::getLabel, request.getKeyword()));
        } else {
            log.info("跳过关键词搜索条件 (基础查询或无关键词)");
        }

        log.info("图像记录查询条件构建完成");
        return wrapper;
    }

    /**
     * 判断是否为基础查询（不应该使用关键词搜索）
     */
    private boolean isBasicQuery(RecordQueryRequest request) {
        String keyword = request.getKeyword();
        if (keyword == null) return true;

        // 如果关键词包含查询相关的词汇，认为是基础查询
        return keyword.contains("查看") || keyword.contains("显示") ||
               keyword.contains("我的") || keyword.contains("检测记录") ||
               keyword.contains("图像检测") || keyword.contains("视频检测") ||
               keyword.contains("摄像头检测");
    }

    /**
     * 构建视频记录查询条件
     */
    private LambdaQueryWrapper<VideoRecords> buildVideoQueryWrapper(RecordQueryRequest request) {
        LambdaQueryWrapper<VideoRecords> wrapper = Wrappers.<VideoRecords>lambdaQuery();

        // 排序
        if ("ASC".equals(request.getSortDirection())) {
            wrapper.orderByAsc(VideoRecords::getStartTime);
        } else {
            wrapper.orderByDesc(VideoRecords::getStartTime);
        }

        // 用户名条件
        if (StrUtil.isNotBlank(request.getUsername())) {
            wrapper.eq(VideoRecords::getUsername, request.getUsername());
        }

        // 作物类型条件
        if (StrUtil.isNotBlank(request.getCropType())) {
            wrapper.eq(VideoRecords::getKind, request.getCropType());
        }

        // 置信度条件
        if (request.getMinConfidence() != null) {
            wrapper.ge(VideoRecords::getConf, request.getMinConfidence().toString());
        }
        if (request.getMaxConfidence() != null) {
            wrapper.le(VideoRecords::getConf, request.getMaxConfidence().toString());
        }

        // 时间范围条件
        if (request.getStartTime() != null) {
            wrapper.ge(VideoRecords::getStartTime, request.getStartTime().format(DATE_FORMATTER));
        }
        if (request.getEndTime() != null) {
            wrapper.le(VideoRecords::getStartTime, request.getEndTime().format(DATE_FORMATTER));
        }

        // 模型文件条件
        if (StrUtil.isNotBlank(request.getModelWeight())) {
            wrapper.eq(VideoRecords::getWeight, request.getModelWeight());
        }

        // 关键词搜索
        if (StrUtil.isNotBlank(request.getKeyword())) {
            wrapper.and(w -> w.like(VideoRecords::getKind, request.getKeyword())
                    .or().like(VideoRecords::getUsername, request.getKeyword()));
        }

        return wrapper;
    }

    /**
     * 构建摄像头记录查询条件
     */
    private LambdaQueryWrapper<CameraRecords> buildCameraQueryWrapper(RecordQueryRequest request) {
        LambdaQueryWrapper<CameraRecords> wrapper = Wrappers.<CameraRecords>lambdaQuery();

        // 排序
        if ("ASC".equals(request.getSortDirection())) {
            wrapper.orderByAsc(CameraRecords::getStartTime);
        } else {
            wrapper.orderByDesc(CameraRecords::getStartTime);
        }

        // 用户名条件
        if (StrUtil.isNotBlank(request.getUsername())) {
            wrapper.eq(CameraRecords::getUsername, request.getUsername());
        }

        // 作物类型条件
        if (StrUtil.isNotBlank(request.getCropType())) {
            wrapper.eq(CameraRecords::getKind, request.getCropType());
        }

        // 置信度条件
        if (request.getMinConfidence() != null) {
            wrapper.ge(CameraRecords::getConf, request.getMinConfidence().toString());
        }
        if (request.getMaxConfidence() != null) {
            wrapper.le(CameraRecords::getConf, request.getMaxConfidence().toString());
        }

        // 时间范围条件
        if (request.getStartTime() != null) {
            wrapper.ge(CameraRecords::getStartTime, request.getStartTime().format(DATE_FORMATTER));
        }
        if (request.getEndTime() != null) {
            wrapper.le(CameraRecords::getStartTime, request.getEndTime().format(DATE_FORMATTER));
        }

        // 模型文件条件
        if (StrUtil.isNotBlank(request.getModelWeight())) {
            wrapper.eq(CameraRecords::getWeight, request.getModelWeight());
        }

        // 关键词搜索
        if (StrUtil.isNotBlank(request.getKeyword())) {
            wrapper.and(w -> w.like(CameraRecords::getKind, request.getKeyword())
                    .or().like(CameraRecords::getUsername, request.getKeyword()));
        }

        return wrapper;
    }

    /**
     * 获取记录类型名称
     */
    private String getRecordTypeName(String recordType) {
        if (recordType == null) return "所有";
        switch (recordType) {
            case "IMG":
                return "图像";
            case "VIDEO":
                return "视频";
            case "CAMERA":
                return "摄像头";
            default:
                return recordType;
        }
    }
}