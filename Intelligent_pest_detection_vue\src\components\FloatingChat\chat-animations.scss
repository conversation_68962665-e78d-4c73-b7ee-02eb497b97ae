// 聊天组件动画样式
// 消息进入动画
// 2025.6.15 nxc 整理
@keyframes messageSlideIn {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

// 消息发送动画
@keyframes messageSending {
	0% {
		opacity: 0.6;
		transform: scale(0.95);
	}
	50% {
		opacity: 0.8;
		transform: scale(1.02);
	}
	100% {
		opacity: 1;
		transform: scale(1);
	}
}

// 打字指示器动画
@keyframes typing {
	0%, 60%, 100% {
		transform: translateY(0);
		opacity: 0.5;
	}
	30% {
		transform: translateY(-10px);
		opacity: 1;
	}
}

// 加载旋转动画
@keyframes spin {
	from { 
		transform: rotate(0deg); 
	}
	to { 
		transform: rotate(360deg); 
	}
}

// 脉冲动画
@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.05);
		opacity: 0.8;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

// 弹跳动画
@keyframes bounce {
	0%, 20%, 53%, 80%, 100% {
		transform: translate3d(0, 0, 0);
	}
	40%, 43% {
		transform: translate3d(0, -8px, 0);
	}
	70% {
		transform: translate3d(0, -4px, 0);
	}
	90% {
		transform: translate3d(0, -2px, 0);
	}
}

// 渐入渐出动画
@keyframes fadeInOut {
	0% {
		opacity: 0;
		transform: scale(0.8);
	}
	50% {
		opacity: 1;
		transform: scale(1.1);
	}
	100% {
		opacity: 1;
		transform: scale(1);
	}
}

// 滑入动画
@keyframes slideInRight {
	from {
		transform: translateX(100%);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

@keyframes slideInLeft {
	from {
		transform: translateX(-100%);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

// 窗口打开动画
@keyframes windowOpen {
	0% {
		opacity: 0;
		transform: scale(0.3) translateY(100px);
	}
	50% {
		opacity: 0.8;
		transform: scale(1.05) translateY(-10px);
	}
	100% {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

// 窗口关闭动画
@keyframes windowClose {
	0% {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
	100% {
		opacity: 0;
		transform: scale(0.3) translateY(100px);
	}
}

// 按钮悬停效果
@keyframes buttonHover {
	0% {
		transform: scale(1);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}
	100% {
		transform: scale(1.05);
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
	}
}

// 通知徽章动画
@keyframes badgePulse {
	0% {
		transform: scale(1);
		background-color: #ff4757;
	}
	50% {
		transform: scale(1.2);
		background-color: #ff3742;
	}
	100% {
		transform: scale(1);
		background-color: #ff4757;
	}
}

// 连接状态指示器
@keyframes connectionPulse {
	0% {
		opacity: 1;
	}
	50% {
		opacity: 0.5;
	}
	100% {
		opacity: 1;
	}
}

// 消息状态动画
@keyframes messageStatus {
	0% {
		opacity: 0;
		transform: scale(0);
	}
	50% {
		opacity: 1;
		transform: scale(1.2);
	}
	100% {
		opacity: 1;
		transform: scale(1);
	}
}

// 错误摇摆动画
@keyframes shake {
	0%, 100% {
		transform: translateX(0);
	}
	10%, 30%, 50%, 70%, 90% {
		transform: translateX(-5px);
	}
	20%, 40%, 60%, 80% {
		transform: translateX(5px);
	}
}

// 成功检查动画
@keyframes checkmark {
	0% {
		stroke-dashoffset: 100;
	}
	100% {
		stroke-dashoffset: 0;
	}
}

// 输入框聚焦动画
@keyframes inputFocus {
	0% {
		border-color: #e9ecef;
		box-shadow: none;
	}
	100% {
		border-color: #667eea;
		box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
	}
}

// 头像呼吸动画
@keyframes avatarBreath {
	0%, 100% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.05);
		opacity: 0.9;
	}
}

// 文字打字机效果
@keyframes typewriter {
	from {
		width: 0;
	}
	to {
		width: 100%;
	}
}

// 光标闪烁
@keyframes blink {
	0%, 50% {
		opacity: 1;
	}
	51%, 100% {
		opacity: 0;
	}
}

// 工具类
.animate-message-in {
	animation: messageSlideIn 0.3s ease-out;
}

.animate-sending {
	animation: messageSending 0.5s ease-in-out;
}

.animate-typing {
	animation: typing 1.4s infinite ease-in-out;
}

.animate-spin {
	animation: spin 1s linear infinite;
}

.animate-pulse {
	animation: pulse 2s infinite;
}

.animate-bounce {
	animation: bounce 1s infinite;
}

.animate-fade-in {
	animation: fadeInOut 0.3s ease-out;
}

.animate-slide-right {
	animation: slideInRight 0.3s ease-out;
}

.animate-slide-left {
	animation: slideInLeft 0.3s ease-out;
}

.animate-window-open {
	animation: windowOpen 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.animate-window-close {
	animation: windowClose 0.3s ease-in;
}

.animate-button-hover {
	animation: buttonHover 0.2s ease-out forwards;
}

.animate-badge-pulse {
	animation: badgePulse 1.5s infinite;
}

.animate-connection-pulse {
	animation: connectionPulse 2s infinite;
}

.animate-shake {
	animation: shake 0.5s ease-in-out;
}

.animate-avatar-breath {
	animation: avatarBreath 3s infinite ease-in-out;
}
