<template>
  <div class="api-test-container">
    <h2>API测试页面</h2>
    
    <!-- 测试按钮 -->
    <div class="test-buttons">
      <el-button type="primary" @click="testImgRecordsAPI">测试图像记录API</el-button>
      <el-button type="success" @click="testImgRecordsWithUser">测试用户过滤API</el-button>
      <el-button type="warning" @click="testDebugAPI">测试调试API</el-button>
    </div>

    <!-- 结果显示 -->
    <div class="results">
      <h3>API测试结果：</h3>
      <el-card v-for="(result, index) in testResults" :key="index" class="result-card">
        <template #header>
          <div class="card-header">
            <span>{{ result.title }}</span>
            <el-tag :type="result.success ? 'success' : 'danger'">
              {{ result.success ? '成功' : '失败' }}
            </el-tag>
          </div>
        </template>
        
        <div class="result-content">
          <p><strong>请求URL:</strong> {{ result.url }}</p>
          <p><strong>请求参数:</strong> {{ JSON.stringify(result.params) }}</p>
          <p><strong>响应状态:</strong> {{ result.status }}</p>
          
          <div class="response-data">
            <h4>响应数据:</h4>
            <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
          </div>
          
          <div v-if="result.error" class="error-info">
            <h4>错误信息:</h4>
            <pre>{{ result.error }}</pre>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import request from '/@/utils/request';

const testResults = ref<any[]>([]);

// 测试图像记录API
const testImgRecordsAPI = async () => {
  const testCase = {
    title: '图像记录API测试',
    url: '/api/imgRecords',
    params: { pageNum: 1, pageSize: 10 },
    success: false,
    status: '',
    data: null,
    error: null
  };

  try {
    console.log('开始测试图像记录API...');
    const response = await request.get('/api/imgRecords', {
      params: testCase.params
    });
    
    testCase.success = response.code === 0;
    testCase.status = `HTTP 200 - Code: ${response.code}`;
    testCase.data = response;
    
    console.log('图像记录API响应:', response);
    ElMessage.success('图像记录API测试完成');
  } catch (error: any) {
    testCase.success = false;
    testCase.status = 'HTTP Error';
    testCase.error = error.message || error.toString();
    
    console.error('图像记录API测试失败:', error);
    ElMessage.error('图像记录API测试失败');
  }

  testResults.value.unshift(testCase);
};

// 测试用户过滤API
const testImgRecordsWithUser = async () => {
  const testCase = {
    title: '用户过滤API测试',
    url: '/api/imgRecords',
    params: { pageNum: 1, pageSize: 10, search: 'nxc' },
    success: false,
    status: '',
    data: null,
    error: null
  };

  try {
    console.log('开始测试用户过滤API...');
    const response = await request.get('/api/imgRecords', {
      params: testCase.params
    });
    
    testCase.success = response.code === 0;
    testCase.status = `HTTP 200 - Code: ${response.code}`;
    testCase.data = response;
    
    console.log('用户过滤API响应:', response);
    
    if (response.code === 0 && response.data && response.data.records) {
      ElMessage.success(`用户过滤API测试完成，返回${response.data.records.length}条记录`);
    } else {
      ElMessage.warning('用户过滤API返回空数据');
    }
  } catch (error: any) {
    testCase.success = false;
    testCase.status = 'HTTP Error';
    testCase.error = error.message || error.toString();
    
    console.error('用户过滤API测试失败:', error);
    ElMessage.error('用户过滤API测试失败');
  }

  testResults.value.unshift(testCase);
};

// 测试调试API
const testDebugAPI = async () => {
  const testCase = {
    title: '调试API测试',
    url: '/api/debug/database',
    params: {},
    success: false,
    status: '',
    data: null,
    error: null
  };

  try {
    console.log('开始测试调试API...');
    const response = await request.get('/api/debug/database');
    
    testCase.success = response.success === true;
    testCase.status = 'HTTP 200';
    testCase.data = response;
    
    console.log('调试API响应:', response);
    ElMessage.success('调试API测试完成');
  } catch (error: any) {
    testCase.success = false;
    testCase.status = 'HTTP Error';
    testCase.error = error.message || error.toString();
    
    console.error('调试API测试失败:', error);
    ElMessage.error('调试API测试失败');
  }

  testResults.value.unshift(testCase);
};
</script>

<style scoped lang="scss">
.api-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    margin-bottom: 20px;
    color: #333;
  }

  .test-buttons {
    margin-bottom: 30px;
    
    .el-button {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }

  .results {
    .result-card {
      margin-bottom: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .result-content {
        p {
          margin: 8px 0;
          
          strong {
            color: #666;
          }
        }

        .response-data, .error-info {
          margin-top: 15px;

          h4 {
            margin: 10px 0;
            color: #333;
          }

          pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
            font-size: 12px;
            line-height: 1.4;
          }
        }

        .error-info pre {
          background: #fef0f0;
          color: #f56c6c;
        }
      }
    }
  }
}
</style>
