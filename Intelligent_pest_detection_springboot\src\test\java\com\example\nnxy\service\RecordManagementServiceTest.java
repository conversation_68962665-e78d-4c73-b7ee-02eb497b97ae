package com.example.nnxy.service;

import com.example.nnxy.dto.RecordQueryRequest;
import com.example.nnxy.dto.RecordOperationResult;
import com.example.nnxy.entity.ImgRecords;
import com.example.nnxy.mapper.ImgRecordsMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 记录管理服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class RecordManagementServiceTest {

    @Autowired
    private RecordManagementService recordManagementService;

    @Autowired
    private ImgRecordsMapper imgRecordsMapper;

    @Test
    public void testDatabaseConnection() {
        // 测试数据库连接和基本查询
        System.out.println("=== 数据库连接测试 ===");
        
        try {
            // 查询所有记录
            List<ImgRecords> allRecords = imgRecordsMapper.selectList(null);
            System.out.println("数据库中总记录数: " + allRecords.size());
            
            // 查询用户nxc的记录
            List<ImgRecords> nxcRecords = imgRecordsMapper.selectList(
                com.baomidou.mybatisplus.core.conditions.query.Wrappers.<ImgRecords>lambdaQuery()
                    .eq(ImgRecords::getUsername, "nxc")
            );
            System.out.println("用户nxc的记录数: " + nxcRecords.size());
            
            // 显示前5条记录
            System.out.println("=== 前5条记录 ===");
            allRecords.stream().limit(5).forEach(record -> {
                System.out.printf("ID: %d, 用户: %s, 作物: %s, 标签: %s, 置信度: %s, 时间: %s%n",
                    record.getId(), record.getUsername(), record.getKind(), 
                    record.getLabel(), record.getConf(), record.getStartTime());
            });
            
            // 显示用户nxc的记录
            System.out.println("=== 用户nxc的记录 ===");
            nxcRecords.forEach(record -> {
                System.out.printf("ID: %d, 用户: %s, 作物: %s, 标签: %s, 置信度: %s, 时间: %s%n",
                    record.getId(), record.getUsername(), record.getKind(), 
                    record.getLabel(), record.getConf(), record.getStartTime());
            });
            
        } catch (Exception e) {
            System.err.println("数据库查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testRecordManagementService() {
        System.out.println("=== 记录管理服务测试 ===");
        
        // 测试查询用户nxc的图像记录
        RecordQueryRequest request = RecordQueryRequest.builder()
            .operationType("QUERY")
            .recordType("IMG")
            .username("nxc")
            .onlyCurrentUser(true)
            .needConfirmation(false)
            .build();
        
        System.out.println("查询请求: " + request);
        
        RecordOperationResult result = recordManagementService.executeOperation(request);
        System.out.println("查询结果: " + result);
        
        if (result.getData() instanceof List) {
            List<?> records = (List<?>) result.getData();
            System.out.println("返回记录数: " + records.size());
        }
    }

    @Test
    public void testQueryAllTypes() {
        System.out.println("=== 查询所有类型记录测试 ===");
        
        // 测试查询所有类型的记录
        RecordQueryRequest request = RecordQueryRequest.builder()
            .operationType("QUERY")
            .recordType(null)  // 查询所有类型
            .username("nxc")
            .onlyCurrentUser(true)
            .needConfirmation(false)
            .build();
        
        System.out.println("查询请求: " + request);
        
        RecordOperationResult result = recordManagementService.executeOperation(request);
        System.out.println("查询结果: " + result);
    }

    @Test
    public void testInsertTestData() {
        System.out.println("=== 插入测试数据 ===");
        
        try {
            // 插入测试数据
            ImgRecords testRecord = new ImgRecords();
            testRecord.setUsername("nxc");
            testRecord.setKind("corn");
            testRecord.setLabel("玉米疫病");
            testRecord.setConf("0.95");
            testRecord.setStartTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            testRecord.setWeight("corn_best.pt");
            
            int result = imgRecordsMapper.insert(testRecord);
            System.out.println("插入结果: " + result);
            System.out.println("插入的记录ID: " + testRecord.getId());
            
            // 验证插入
            ImgRecords inserted = imgRecordsMapper.selectById(testRecord.getId());
            System.out.println("验证插入的记录: " + inserted);
            
        } catch (Exception e) {
            System.err.println("插入测试数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testQueryWithoutUser() {
        System.out.println("=== 不限用户查询测试 ===");
        
        // 测试不限用户的查询
        RecordQueryRequest request = RecordQueryRequest.builder()
            .operationType("QUERY")
            .recordType("IMG")
            .username(null)  // 不限用户
            .onlyCurrentUser(false)
            .needConfirmation(false)
            .build();
        
        System.out.println("查询请求: " + request);
        
        RecordOperationResult result = recordManagementService.executeOperation(request);
        System.out.println("查询结果: " + result);
    }

    @Test
    public void testQueryWithKeyword() {
        System.out.println("=== 关键词查询测试 ===");
        
        // 测试关键词查询
        RecordQueryRequest request = RecordQueryRequest.builder()
            .operationType("QUERY")
            .recordType("IMG")
            .username("nxc")
            .keyword("玉米")
            .onlyCurrentUser(true)
            .needConfirmation(false)
            .build();
        
        System.out.println("查询请求: " + request);
        
        RecordOperationResult result = recordManagementService.executeOperation(request);
        System.out.println("查询结果: " + result);
    }
}
