package com.example.nnxy.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 记录查询请求DTO
 * <AUTHOR>
 */
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class RecordQueryRequest {
    
    /**
     * 操作类型：QUERY, DELETE, COUNT, EXPORT
     */
    private String operationType;
    
    /**
     * 记录类型：IMG, VIDEO, CAMERA
     */
    private String recordType;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 作物类型
     */
    private String cropType;
    
    /**
     * 检测标签
     */
    private String label;
    
    /**
     * 置信度阈值范围
     */
    private Double minConfidence;
    private Double maxConfidence;
    
    /**
     * 时间范围
     */
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    
    /**
     * 时间范围描述（今天、昨天、本周、本月等）
     */
    private String timeRange;
    
    /**
     * 模型文件名
     */
    private String modelWeight;
    
    /**
     * 分页参数
     */
    private Integer pageNum = 1;
    private Integer pageSize = 10;
    
    /**
     * 排序字段
     */
    private String sortField = "startTime";
    
    /**
     * 排序方向：ASC, DESC
     */
    private String sortDirection = "DESC";
    
    /**
     * 是否只查询当前用户的记录
     */
    private Boolean onlyCurrentUser = true;
    
    /**
     * 特定的记录ID列表（用于批量操作）
     */
    private List<Integer> recordIds;
    
    /**
     * 是否需要确认删除操作
     */
    private Boolean needConfirmation = true;
    
    /**
     * 查询关键词（模糊搜索）
     */
    private String keyword;
    
    /**
     * 是否包含详细信息
     */
    private Boolean includeDetails = false;
    
    /**
     * 限制返回结果数量
     */
    private Integer limit;

    /**
     * 是否生成分析报告
     */
    private Boolean generateReport = false;

    /**
     * 报告类型：SUMMARY(摘要), DETAILED(详细), TREND(趋势分析)
     */
    private String reportType = "SUMMARY";

    /**
     * 分析维度：DISEASE(病害分析), CONFIDENCE(置信度分析), TIME(时间分析)
     */
    private List<String> analysisDimensions;
}
