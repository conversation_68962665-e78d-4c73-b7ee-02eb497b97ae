<template>
	<div class="pest-login-container">
		<!-- 背景装饰 -->
		<div class="bg-decoration">
			<div class="decoration-circle circle-1"></div>
			<div class="decoration-circle circle-2"></div>
			<div class="decoration-circle circle-3"></div>
			<div class="decoration-grid"></div>
		</div>

		<!-- 主登录卡片 -->
		<div class="login-card pest-card animate-fade-in-up">
			<!-- 系统标题区域 -->
			<div class="login-header">
				<div class="logo-section">
					<div class="logo-container">
						<img src="/src/assets/Logo.png" alt="YOLO11智能虫害检测系统" class="system-logo" />
					</div>
					<div class="system-info">
						<h1 class="system-title">YOLO11智能虫害检测系统</h1>
						<p class="system-subtitle">YOLO11 Intelligent Pest Detection System</p>
					</div>
				</div>
			</div>

			<!-- 登录表单区域 -->
			<div class="login-form-section">
				<h2 class="form-title">系统登录</h2>
				<p class="form-subtitle">请输入您的账号信息</p>

				<el-form :model="ruleForm" :rules="registerRules" ref="ruleFormRef" class="login-form">
					<el-form-item prop="username" class="animate-fade-in-left animate-delay-100">
						<div class="input-wrapper">
							<el-input
								v-model="ruleForm.username"
								placeholder="请输入用户名"
								prefix-icon="User"
								class="pest-input"
								size="large"
							/>
						</div>
					</el-form-item>

					<el-form-item prop="password" class="animate-fade-in-right animate-delay-200">
						<div class="input-wrapper">
							<el-input
								v-model="ruleForm.password"
								type="password"
								placeholder="请输入密码"
								prefix-icon="Lock"
								show-password
								class="pest-input"
								size="large"
							/>
						</div>
					</el-form-item>

					<el-form-item class="animate-fade-in-up animate-delay-300">
						<el-button
							type="primary"
							class="pest-btn-primary login-submit-btn"
							@click="submitForm(ruleFormRef)"
							size="large"
						>
							<i class="iconfont icon-denglu" style="margin-right: 8px;"></i>
							立即登录
						</el-button>
					</el-form-item>
				</el-form>

				<!-- 操作选项 -->
				<div class="login-options animate-fade-in-up animate-delay-400">
					<router-link to="/register" class="option-link">
						<i class="iconfont icon-yonghu" style="margin-right: 4px;"></i>
						注册账号
					</router-link>
					<span class="divider">|</span>
					<a href="#" class="option-link">
						<i class="iconfont icon-wangji" style="margin-right: 4px;"></i>
						忘记密码
					</a>
				</div>
			</div>

			<!-- 功能特色展示 -->
			<div class="features-section animate-fade-in-up animate-delay-500">
				<div class="feature-item">
					<i class="iconfont icon-tupian"></i>
					<span>图像检测</span>
				</div>
				<div class="feature-item">
					<i class="iconfont icon-shipin1"></i>
					<span>视频分析</span>
				</div>
				<div class="feature-item">
					<i class="iconfont icon-shexiangtou1"></i>
					<span>实时监控</span>
				</div>
				<div class="feature-item">
					<i class="iconfont icon-baogao"></i>
					<span>智能报告</span>
				</div>
			</div>
		</div>

		<!-- 页脚信息 -->
		<div class="login-footer animate-fade-in-up animate-delay-600">
			<p>&copy; 2024 YOLO11智能虫害检测系统. All rights reserved.</p>
			<p>Powered by Vue 3 + TypeScript + Element Plus</p>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import Cookies from 'js-cookie';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { initFrontEndControlRoutes } from '/@/router/frontEnd';
import { initBackEndControlRoutes } from '/@/router/backEnd';
import { Session } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';
import type { FormInstance, FormRules } from 'element-plus';
import request from '/@/utils/request';

// 定义变量内容
const { t } = useI18n();
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const route = useRoute();
const router = useRouter();
const formSize = ref('default');
const ruleFormRef = ref<FormInstance>();

/*
 * 定义全局变量，等价Vue2中的data() return。
 */
const ruleForm = reactive({
	username: '',
	password: '',
});

/*
 * 校验规则。
 */
const registerRules = reactive<FormRules>({
	username: [
		{ required: true, message: '请输入账号', trigger: 'blur' },
		{ min: 3, max: 5, message: '长度在3-5个字符', trigger: 'blur' },
	],
	password: [
		{ required: true, message: '请输入密码', trigger: 'blur' },
		{ min: 3, max: 5, message: '长度在3-5个字符', trigger: 'blur' },
	],
});

/*
 * 提交后的方法。
 */
// 时间获取
const currentTime = computed(() => {
	return formatAxis(new Date());
});
// 登录
const onSignIn = async () => {
	// 存储 token 到浏览器缓存
	Session.set('token', Math.random().toString(36).substr(0));
	// 模拟数据，对接接口时，记得删除多余代码及对应依赖的引入。用于 `/src/stores/userInfo.ts` 中不同用户登录判断（模拟数据）
	Cookies.set('userName', ruleForm.username);
	if (!themeConfig.value.isRequestRoutes) {
		// 前端控制路由，2、请注意执行顺序
		const isNoPower = await initFrontEndControlRoutes();
		signInSuccess(isNoPower);
	} else {
		// 模拟后端控制路由，isRequestRoutes 为 true，则开启后端控制路由
		// 添加完动态路由，再进行 router 跳转，否则可能报错 No match found for location with path "/"
		const isNoPower = await initBackEndControlRoutes();
		// 执行完 initBackEndControlRoutes，再执行 signInSuccess
		signInSuccess(isNoPower);
	}
};
// 登录成功后的跳转
const signInSuccess = (isNoPower: boolean | undefined) => {
	if (isNoPower) {
		ElMessage.warning('抱歉，您没有登录权限');
		Session.clear();
	} else {
		// 初始化登录成功时间问候语
		let currentTimeInfo = currentTime.value;
		// 登录成功，跳到转首页
		// 如果是复制粘贴的路径，非首页/登录页，那么登录成功后重定向到对应的路径中
		if (route.query?.redirect) {
			router.push({
				path: <string>route.query?.redirect,
				query: Object.keys(<string>route.query?.params).length > 0 ? JSON.parse(<string>route.query?.params) : '',
			});
		} else {
			router.push('/');
		}
		// 登录成功提示
		const signInText = t('message.signInText');
		ElMessage.success(`${currentTimeInfo}，${signInText}`);
		// 添加 loading，防止第一次进入界面时出现短暂空白
		NextLoading.start();
	}
};
const submitForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.validate((valid) => {
		if (valid) {
			request.post('/api/user/login', ruleForm).then((res) => {
				console.log(res);
				if (res.code == 0) {
					Cookies.set('role', res.data.role); //  设置角色
					//登录成功
					onSignIn();
				} else {
					ElMessage({
						type: 'error',
						message: res.msg,
					});
				}
			});
		} else {
			console.log('error submit!');
			return false;
		}
	});
};
</script>

<style scoped>
.pest-login-container {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: var(--bg-gradient);
	padding: var(--spacing-lg);
	position: relative;
	overflow: hidden;
}

.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
	pointer-events: none;
}

.decoration-circle {
	position: absolute;
	border-radius: 50%;
	background: var(--primary-gradient);
	opacity: 0.1;
	animation: float 6s ease-in-out infinite;
}

.circle-1 {
	width: 200px;
	height: 200px;
	top: 10%;
	left: 10%;
	animation-delay: 0s;
}

.circle-2 {
	width: 150px;
	height: 150px;
	top: 60%;
	right: 15%;
	animation-delay: 2s;
}

.circle-3 {
	width: 100px;
	height: 100px;
	bottom: 20%;
	left: 20%;
	animation-delay: 4s;
}

.decoration-grid {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-image:
		linear-gradient(rgba(30, 58, 138, 0.03) 1px, transparent 1px),
		linear-gradient(90deg, rgba(30, 58, 138, 0.03) 1px, transparent 1px);
	background-size: 50px 50px;
}

.login-card {
	position: relative;
	z-index: 2;
	width: 100%;
	max-width: 480px;
	padding: var(--spacing-2xl);
	background: var(--bg-card);
	border-radius: var(--radius-xl);
	box-shadow: var(--shadow-xl);
	backdrop-filter: blur(10px);
}

.login-header {
	text-align: center;
	margin-bottom: var(--spacing-xl);
}

.logo-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: var(--spacing-md);
}

.logo-container {
	width: 120px;
	height: 120px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: var(--radius-xl);
	background: var(--bg-card);
	box-shadow: var(--shadow-lg);
	padding: var(--spacing-md);
	animation: pulse 2s infinite;
}

.system-logo {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.system-info {
	text-align: center;
}

.system-title {
	font-size: 1.75rem;
	color: var(--text-primary);
	margin-bottom: var(--spacing-sm);
	font-weight: 700;
	background: var(--primary-gradient);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.system-subtitle {
	font-size: 0.875rem;
	color: var(--text-secondary);
	letter-spacing: 1px;
	font-weight: 500;
}

.login-form-section {
	margin-bottom: var(--spacing-xl);
}

.form-title {
	font-size: 1.5rem;
	color: var(--text-primary);
	margin-bottom: var(--spacing-sm);
	font-weight: 600;
	text-align: center;
}

.form-subtitle {
	color: var(--text-secondary);
	text-align: center;
	margin-bottom: var(--spacing-xl);
	font-size: 0.875rem;
}

.login-form {
	margin-bottom: var(--spacing-lg);
}

.input-wrapper {
	position: relative;
}

:deep(.pest-input .el-input__wrapper) {
	border: 2px solid var(--border-light);
	border-radius: var(--radius-md);
	padding: var(--spacing-md) var(--spacing-lg);
	background: var(--bg-card);
	transition: all var(--transition-normal);
	box-shadow: var(--shadow-sm);
}

:deep(.pest-input .el-input__wrapper:hover) {
	border-color: var(--border-medium);
	box-shadow: var(--shadow-md);
}

:deep(.pest-input .el-input__wrapper.is-focus) {
	border-color: var(--primary-light);
	box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
	background: var(--bg-card);
}

.login-submit-btn {
	width: 100%;
	height: 48px;
	font-size: 1rem;
	font-weight: 600;
	letter-spacing: 1px;
	margin-top: var(--spacing-md);
}

.login-options {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: var(--spacing-md);
	margin-bottom: var(--spacing-xl);
}

.option-link {
	color: var(--primary-color);
	text-decoration: none;
	font-size: 0.875rem;
	font-weight: 500;
	transition: all var(--transition-normal);
	display: flex;
	align-items: center;
}

.option-link:hover {
	color: var(--primary-light);
	transform: translateY(-1px);
}

.divider {
	color: var(--border-medium);
	font-weight: 300;
}

.features-section {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: var(--spacing-md);
	padding: var(--spacing-lg);
	background: var(--bg-secondary);
	border-radius: var(--radius-lg);
	margin-bottom: var(--spacing-lg);
}

.feature-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: var(--spacing-sm);
	padding: var(--spacing-md);
	transition: all var(--transition-normal);
	border-radius: var(--radius-md);
}

.feature-item:hover {
	background: var(--bg-card);
	transform: translateY(-2px);
	box-shadow: var(--shadow-md);
}

.feature-item i {
	font-size: 1.5rem;
	color: var(--primary-color);
}

.feature-item span {
	font-size: 0.75rem;
	color: var(--text-secondary);
	font-weight: 500;
}

.login-footer {
	text-align: center;
	margin-top: var(--spacing-xl);
	color: var(--text-muted);
	font-size: 0.75rem;
	line-height: 1.5;
}

/* 动画效果 */
@keyframes float {
	0%, 100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-20px);
	}
}

/* 响应式适配 */
@media (max-width: 768px) {
	.pest-login-container {
		padding: var(--spacing-md);
	}

	.login-card {
		padding: var(--spacing-xl);
		max-width: 100%;
	}

	.system-title {
		font-size: 1.5rem;
	}

	.system-subtitle {
		font-size: 0.75rem;
	}

	.features-section {
		grid-template-columns: repeat(2, 1fr);
		gap: var(--spacing-sm);
		padding: var(--spacing-md);
	}

	.feature-item {
		padding: var(--spacing-sm);
	}

	.feature-item i {
		font-size: 1.25rem;
	}

	.feature-item span {
		font-size: 0.625rem;
	}

	.decoration-circle {
		display: none;
	}
}


</style>
