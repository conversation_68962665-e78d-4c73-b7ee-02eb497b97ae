package com.example.nnxy.service;

import cn.hutool.core.util.StrUtil;
import com.example.nnxy.dto.RecordQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * AI指令解析服务
 * 将用户的自然语言指令转换为具体的数据库操作参数
 * <AUTHOR>
 */
@Slf4j
@Service
public class CommandParserService {

    @Resource
    private ReportAnalysisService reportAnalysisService;
    
    // 操作类型关键词
    private static final List<String> QUERY_KEYWORDS = Arrays.asList(
        "查询", "查看", "搜索", "找", "显示", "列出", "获取", "检索"
    );
    
    private static final List<String> DELETE_KEYWORDS = Arrays.asList(
        "删除", "清除", "移除", "清理", "清空"
    );
    
    private static final List<String> COUNT_KEYWORDS = Arrays.asList(
        "统计", "计算", "数量", "总数", "多少", "几个", "几条"
    );

    private static final List<String> REPORT_KEYWORDS = Arrays.asList(
        "报告", "分析", "分析报告", "生成报告", "导出报告", "专业分析", "详细分析", "趋势分析"
    );
    
    // 记录类型关键词
    private static final List<String> IMG_KEYWORDS = Arrays.asList(
        "图像", "图片", "照片", "图像检测", "图片检测"
    );
    
    private static final List<String> VIDEO_KEYWORDS = Arrays.asList(
        "视频", "录像", "视频检测"
    );
    
    private static final List<String> CAMERA_KEYWORDS = Arrays.asList(
        "摄像头", "实时", "摄像头检测", "实时检测"
    );
    
    // 作物类型关键词
    private static final List<String> CROP_TYPES = Arrays.asList(
        "corn", "玉米", "rice", "水稻", "strawberry", "草莓", "tomato", "西红柿", "番茄"
    );
    
    // 时间范围关键词
    private static final List<String> TIME_KEYWORDS = Arrays.asList(
        "今天", "昨天", "前天", "本周", "上周", "本月", "上月", "最近"
    );

    // 确认关键词
    private static final List<String> CONFIRM_KEYWORDS = Arrays.asList(
        "确认", "是的", "好的", "确定", "同意", "继续", "执行", "删除"
    );

    // 取消关键词
    private static final List<String> CANCEL_KEYWORDS = Arrays.asList(
        "取消", "不", "不要", "停止", "算了", "不删除", "不执行"
    );
    
    /**
     * 解析用户指令
     */
    public RecordQueryRequest parseCommand(String command, String username) {
        log.info("开始解析用户指令: {}", command);
        
        RecordQueryRequest request = RecordQueryRequest.builder()
                .username(username)
                .onlyCurrentUser(true)
                .needConfirmation(false)  // 默认不需要确认，删除操作时会重新设置
                .build();
        
        // 转换为小写便于匹配
        String lowerCommand = command.toLowerCase();
        
        // 解析操作类型
        parseOperationType(lowerCommand, request);
        
        // 解析记录类型
        parseRecordType(lowerCommand, request);
        
        // 解析作物类型
        parseCropType(lowerCommand, request);
        
        // 解析时间范围
        parseTimeRange(lowerCommand, request);
        
        // 解析置信度
        parseConfidence(lowerCommand, request);
        
        // 解析用户名
        parseTargetUsername(lowerCommand, request);
        
        // 解析数量限制
        parseLimit(lowerCommand, request);
        
        // 解析关键词
        parseKeyword(command, request);

        // 解析报告生成需求
        parseReportGeneration(lowerCommand, request);

        log.info("指令解析完成: {}", request);
        return request;
    }
    
    /**
     * 解析操作类型
     */
    private void parseOperationType(String command, RecordQueryRequest request) {
        if (containsAny(command, DELETE_KEYWORDS)) {
            request.setOperationType("DELETE");
            request.setNeedConfirmation(true);  // 删除操作需要确认
        } else if (containsAny(command, REPORT_KEYWORDS)) {
            request.setOperationType("REPORT");
            request.setGenerateReport(true);
        } else if (containsAny(command, COUNT_KEYWORDS)) {
            request.setOperationType("COUNT");
        } else if (containsAny(command, QUERY_KEYWORDS)) {
            request.setOperationType("QUERY");
        } else {
            // 默认为查询操作
            request.setOperationType("QUERY");
        }
    }
    
    /**
     * 解析记录类型
     */
    private void parseRecordType(String command, RecordQueryRequest request) {
        if (containsAny(command, IMG_KEYWORDS)) {
            request.setRecordType("IMG");
        } else if (containsAny(command, VIDEO_KEYWORDS)) {
            request.setRecordType("VIDEO");
        } else if (containsAny(command, CAMERA_KEYWORDS)) {
            request.setRecordType("CAMERA");
        }
        // 如果没有指定类型，则查询所有类型
    }
    
    /**
     * 解析作物类型
     */
    private void parseCropType(String command, RecordQueryRequest request) {
        for (String crop : CROP_TYPES) {
            if (command.contains(crop)) {
                // 标准化作物名称
                if (crop.equals("玉米")) {
                    request.setCropType("corn");
                } else if (crop.equals("水稻")) {
                    request.setCropType("rice");
                } else if (crop.equals("草莓")) {
                    request.setCropType("strawberry");
                } else if (crop.equals("西红柿") || crop.equals("番茄")) {
                    request.setCropType("tomato");
                } else {
                    request.setCropType(crop);
                }
                break;
            }
        }
    }
    
    /**
     * 解析时间范围
     */
    private void parseTimeRange(String command, RecordQueryRequest request) {
        LocalDateTime now = LocalDateTime.now();
        
        if (command.contains("今天")) {
            request.setStartTime(now.toLocalDate().atStartOfDay());
            request.setEndTime(now);
            request.setTimeRange("今天");
        } else if (command.contains("昨天")) {
            LocalDateTime yesterday = now.minusDays(1);
            request.setStartTime(yesterday.toLocalDate().atStartOfDay());
            request.setEndTime(yesterday.toLocalDate().atTime(23, 59, 59));
            request.setTimeRange("昨天");
        } else if (command.contains("本周")) {
            LocalDateTime weekStart = now.minusDays(now.getDayOfWeek().getValue() - 1).toLocalDate().atStartOfDay();
            request.setStartTime(weekStart);
            request.setEndTime(now);
            request.setTimeRange("本周");
        } else if (command.contains("本月")) {
            LocalDateTime monthStart = now.withDayOfMonth(1).toLocalDate().atStartOfDay();
            request.setStartTime(monthStart);
            request.setEndTime(now);
            request.setTimeRange("本月");
        } else if (command.contains("最近")) {
            // 解析最近几天
            Pattern pattern = Pattern.compile("最近(\\d+)天");
            Matcher matcher = pattern.matcher(command);
            if (matcher.find()) {
                int days = Integer.parseInt(matcher.group(1));
                request.setStartTime(now.minusDays(days));
                request.setEndTime(now);
                request.setTimeRange("最近" + days + "天");
            } else {
                // 默认最近7天
                request.setStartTime(now.minusDays(7));
                request.setEndTime(now);
                request.setTimeRange("最近7天");
            }
        }
    }
    
    /**
     * 解析置信度
     */
    private void parseConfidence(String command, RecordQueryRequest request) {
        // 匹配置信度范围，如"置信度大于80%"、"置信度在70%-90%之间"
        Pattern pattern1 = Pattern.compile("置信度[大于>]+(\\d+)%?");
        Matcher matcher1 = pattern1.matcher(command);
        if (matcher1.find()) {
            double confidence = Double.parseDouble(matcher1.group(1)) / 100.0;
            request.setMinConfidence(confidence);
        }
        
        Pattern pattern2 = Pattern.compile("置信度[小于<]+(\\d+)%?");
        Matcher matcher2 = pattern2.matcher(command);
        if (matcher2.find()) {
            double confidence = Double.parseDouble(matcher2.group(1)) / 100.0;
            request.setMaxConfidence(confidence);
        }
        
        Pattern pattern3 = Pattern.compile("置信度.*?(\\d+)%?[到-](\\d+)%?");
        Matcher matcher3 = pattern3.matcher(command);
        if (matcher3.find()) {
            double minConf = Double.parseDouble(matcher3.group(1)) / 100.0;
            double maxConf = Double.parseDouble(matcher3.group(2)) / 100.0;
            request.setMinConfidence(minConf);
            request.setMaxConfidence(maxConf);
        }
    }
    
    /**
     * 解析目标用户名
     */
    private void parseTargetUsername(String command, RecordQueryRequest request) {
        Pattern pattern = Pattern.compile("用户[名]?[是为]?[\"']?([\\w\\u4e00-\\u9fa5]+)[\"']?的?记录");
        Matcher matcher = pattern.matcher(command);
        if (matcher.find()) {
            String targetUser = matcher.group(1);
            if (!targetUser.equals(request.getUsername())) {
                request.setUsername(targetUser);
                request.setOnlyCurrentUser(false);
            }
        }
    }
    
    /**
     * 解析数量限制
     */
    private void parseLimit(String command, RecordQueryRequest request) {
        Pattern pattern = Pattern.compile("(?:前|最新|最近|显示|查看)\\s*(\\d+)\\s*(?:条|个|项)");
        Matcher matcher = pattern.matcher(command);
        if (matcher.find()) {
            int limit = Integer.parseInt(matcher.group(1));
            request.setLimit(limit);
            request.setPageSize(Math.min(limit, 50)); // 限制最大页面大小
        }
    }
    
    /**
     * 解析报告生成需求
     */
    private void parseReportGeneration(String command, RecordQueryRequest request) {
        if (containsAny(command, REPORT_KEYWORDS)) {
            request.setGenerateReport(true);

            // 解析报告类型
            if (command.contains("详细") || command.contains("详细分析")) {
                request.setReportType("DETAILED");
            } else if (command.contains("趋势") || command.contains("趋势分析")) {
                request.setReportType("TREND");
            } else {
                request.setReportType("SUMMARY");
            }

            // 解析分析维度
            List<String> dimensions = new ArrayList<>();
            if (command.contains("病害") || command.contains("疾病")) {
                dimensions.add("DISEASE");
            }
            if (command.contains("置信度")) {
                dimensions.add("CONFIDENCE");
            }
            if (command.contains("时间") || command.contains("趋势")) {
                dimensions.add("TIME");
            }

            // 如果没有指定维度，默认包含所有维度
            if (dimensions.isEmpty()) {
                dimensions.addAll(Arrays.asList("DISEASE", "CONFIDENCE", "TIME"));
            }

            request.setAnalysisDimensions(dimensions);
        }
    }

    /**
     * 解析关键词
     */
    private void parseKeyword(String command, RecordQueryRequest request) {
        // 对于基础查询指令，不设置关键词搜索
        if (isBasicQueryCommand(command)) {
            log.info("识别为基础查询指令，跳过关键词解析");
            return;
        }

        // 如果已经解析出了作物类型，不再使用关键词搜索
        if (StrUtil.isNotBlank(request.getCropType())) {
            log.info("已解析出作物类型: {}，跳过关键词搜索", request.getCropType());
            return;
        }

        // 提取真正的搜索关键词（排除查询相关词汇）
        String[] words = command.split("[\\s，,。.！!？?]");
        List<String> keywords = new ArrayList<>();

        for (String word : words) {
            word = word.trim();
            if (word.length() > 1 && !isCommonWord(word) && !isQueryWord(word) && isValidKeyword(word)) {
                keywords.add(word);
            }
        }

        if (!keywords.isEmpty()) {
            String keyword = String.join(" ", keywords);
            log.info("提取的搜索关键词: {}", keyword);
            request.setKeyword(keyword);
        } else {
            log.info("未提取到有效的搜索关键词");
        }
    }

    /**
     * 检查是否为基础查询指令
     */
    private boolean isBasicQueryCommand(String command) {
        List<String> basicPatterns = Arrays.asList(
            "查看我的", "显示我的", "我的记录", "检测记录", "图像检测记录", "视频检测记录", "摄像头检测记录",
            "今天的", "昨天的", "本周的", "本月的", "最近"
        );
        return basicPatterns.stream().anyMatch(command::contains);
    }

    /**
     * 检查是否为查询相关词汇
     */
    private boolean isQueryWord(String word) {
        List<String> queryWords = Arrays.asList(
            "查看", "显示", "检测", "记录", "图像", "视频", "摄像头", "我的", "搜索", "相关", "的"
        );
        return queryWords.contains(word);
    }

    /**
     * 检查是否为有效的搜索关键词
     */
    private boolean isValidKeyword(String word) {
        // 只有病害名称、作物名称等才是有效关键词
        List<String> validKeywords = Arrays.asList(
            // 病害名称关键词
            "疫病", "锈病", "稻瘟", "炭疽", "花枯", "灰霉", "晚疫", "潜叶", "健康",
            // 作物名称关键词
            "玉米", "水稻", "草莓", "西红柿", "番茄",
            // 英文作物名称
            "corn", "rice", "strawberry", "tomato"
        );
        return validKeywords.stream().anyMatch(keyword -> word.contains(keyword) || keyword.contains(word));
    }
    
    /**
     * 检查是否包含任意关键词
     */
    private boolean containsAny(String text, List<String> keywords) {
        return keywords.stream().anyMatch(text::contains);
    }
    
    /**
     * 检查是否为常见词汇（过滤掉）
     */
    private boolean isCommonWord(String word) {
        List<String> commonWords = Arrays.asList(
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "个", "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这", "那", "什么", "可以", "怎么", "为什么", "哪里", "什么时候",
            "查看", "显示", "检测", "记录", "图像", "视频", "摄像头", "我的", "今天", "昨天", "本周", "本月", "最近"
        );
        return commonWords.contains(word.toLowerCase());
    }
    
    /**
     * 判断是否为报告生成指令
     */
    public boolean isReportGenerationCommand(String command) {
        String lowerCommand = command.toLowerCase();

        // 检查是否包含报告生成关键词
        return containsAny(lowerCommand, REPORT_KEYWORDS) &&
               (containsAny(lowerCommand, CROP_TYPES) ||
                containsAny(lowerCommand, TIME_KEYWORDS) ||
                lowerCommand.contains("检测") ||
                lowerCommand.contains("病害"));
    }

    /**
     * 判断是否为记录管理相关指令
     */
    public boolean isRecordManagementCommand(String command) {
        String lowerCommand = command.toLowerCase();
        
        // 检查是否包含记录管理相关关键词
        List<String> recordKeywords = Arrays.asList("记录", "检测", "历史", "数据");
        List<String> operationKeywords = new ArrayList<>();
        operationKeywords.addAll(QUERY_KEYWORDS);
        operationKeywords.addAll(DELETE_KEYWORDS);
        operationKeywords.addAll(COUNT_KEYWORDS);
        
        return containsAny(lowerCommand, recordKeywords) && containsAny(lowerCommand, operationKeywords);
    }

    /**
     * 判断是否为确认指令
     */
    public boolean isConfirmCommand(String command) {
        String lowerCommand = command.toLowerCase();
        return containsAny(lowerCommand, CONFIRM_KEYWORDS);
    }

    /**
     * 判断是否为取消指令
     */
    public boolean isCancelCommand(String command) {
        String lowerCommand = command.toLowerCase();
        return containsAny(lowerCommand, CANCEL_KEYWORDS);
    }

    /**
     * 解析确认删除指令
     */
    public RecordQueryRequest parseConfirmDeleteCommand(String command, String username,
                                                       RecordQueryRequest previousRequest) {
        if (isConfirmCommand(command)) {
            // 确认删除，设置不需要再次确认
            previousRequest.setNeedConfirmation(false);
            return previousRequest;
        } else if (isCancelCommand(command)) {
            // 取消删除，返回null表示取消操作
            return null;
        } else {
            // 不是确认或取消指令，重新解析
            return parseCommand(command, username);
        }
    }

    /**
     * 生成智能建议
     */
    public List<String> generateSuggestions(String command) {
        List<String> suggestions = new ArrayList<>();
        String lowerCommand = command.toLowerCase();

        if (containsAny(lowerCommand, QUERY_KEYWORDS)) {
            suggestions.add("查看我今天的图像检测记录");
            suggestions.add("搜索玉米相关的检测记录");
            suggestions.add("显示置信度大于80%的记录");
            suggestions.add("查找最近7天的检测记录");
        }

        if (containsAny(lowerCommand, COUNT_KEYWORDS)) {
            suggestions.add("统计本周的检测数量");
            suggestions.add("计算草莓检测记录有多少条");
            suggestions.add("统计置信度大于90%的记录数量");
        }

        if (containsAny(lowerCommand, DELETE_KEYWORDS)) {
            suggestions.add("删除昨天的检测记录");
            suggestions.add("清除置信度低于50%的记录");
            suggestions.add("删除指定ID的记录");
        }

        // 如果没有匹配的建议，提供通用建议
        if (suggestions.isEmpty()) {
            suggestions.add("查看我的检测记录");
            suggestions.add("统计检测数量");
            suggestions.add("搜索特定作物的记录");
        }

        return suggestions;
    }

    /**
     * 验证删除操作的安全性
     */
    public boolean isDeleteOperationSafe(RecordQueryRequest request) {
        // 检查是否有足够的限制条件
        int conditionCount = 0;

        if (request.getStartTime() != null || request.getEndTime() != null) {
            conditionCount++;
        }
        if (request.getCropType() != null) {
            conditionCount++;
        }
        if (request.getMinConfidence() != null || request.getMaxConfidence() != null) {
            conditionCount++;
        }
        if (request.getRecordIds() != null && !request.getRecordIds().isEmpty()) {
            conditionCount++;
        }
        if (request.getUsername() != null) {
            conditionCount++;
        }

        // 至少需要一个限制条件才能执行删除
        return conditionCount > 0;
    }
}
