package com.example.nnxy.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 分析报告DTO
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AnalysisReportDTO {
    
    /**
     * 报告ID
     */
    private String reportId;
    
    /**
     * 报告标题
     */
    private String title;
    
    /**
     * 报告类型
     */
    private String reportType;
    
    /**
     * 生成时间
     */
    private LocalDateTime generateTime;
    
    /**
     * 查询条件摘要
     */
    private String queryConditions;
    
    /**
     * 作物类型
     */
    private String cropType;
    
    /**
     * 分析时间范围
     */
    private String timeRange;
    
    /**
     * 总记录数
     */
    private Long totalRecords;
    
    /**
     * 执行摘要
     */
    private ExecutiveSummary executiveSummary;
    
    /**
     * 病害分析
     */
    private DiseaseAnalysis diseaseAnalysis;
    
    /**
     * 置信度分析
     */
    private ConfidenceAnalysis confidenceAnalysis;
    
    /**
     * 时间趋势分析
     */
    private TrendAnalysis trendAnalysis;
    
    /**
     * 建议和结论
     */
    private List<String> recommendations;
    
    /**
     * 详细数据
     */
    private List<Object> detailData;
    
    /**
     * 统计图表数据
     */
    private Map<String, Object> chartData;
    
    /**
     * 执行摘要
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExecutiveSummary {
        private String overview;
        private Map<String, Object> keyMetrics;
        private List<String> highlights;
    }
    
    /**
     * 病害分析
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DiseaseAnalysis {
        private Map<String, Integer> diseaseDistribution;
        private String mostCommonDisease;
        private Double diseaseRate;
        private List<DiseaseDetail> diseaseDetails;
        
        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class DiseaseDetail {
            private String diseaseName;
            private Integer count;
            private Double percentage;
            private Double avgConfidence;
            private String severity;
            private String description;
        }
    }
    
    /**
     * 置信度分析
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ConfidenceAnalysis {
        private Double averageConfidence;
        private Double highConfidenceRate;
        private Double mediumConfidenceRate;
        private Double lowConfidenceRate;
        private Map<String, Double> confidenceByDisease;
    }
    
    /**
     * 趋势分析
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TrendAnalysis {
        private List<TimePoint> timePoints;
        private String trend;
        private String seasonalPattern;
        
        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class TimePoint {
            private String date;
            private Integer count;
            private Map<String, Integer> diseaseCount;
        }
    }
}
