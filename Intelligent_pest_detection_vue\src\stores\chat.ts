import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { ChatApi, ChatMessage, ChatRequest, ChatUtils, RecordOperationResult } from '/@/api/chat';
import { ElMessage } from 'element-plus';

/**
 * 聊天状态管理
 */
export const useChatStore = defineStore('chat', () => {
	// 状态
	const isOpen = ref(false);
	const isMinimized = ref(false);
	const isLoading = ref(false);
	const isTyping = ref(false);
	const isOnline = ref(true);
	const unreadCount = ref(0);
	const currentSessionId = ref('');
	const currentSessionUser = ref('');
	const messages = ref<ChatMessage[]>([]);
	const position = ref({ x: 20, y: 20 });

	// 计算属性
	const hasMessages = computed(() => messages.value.length > 0);
	const lastMessage = computed(() => messages.value[messages.value.length - 1]);
	const userMessages = computed(() => messages.value.filter(msg => msg.role === 'user'));
	const aiMessages = computed(() => messages.value.filter(msg => msg.role === 'assistant'));

	/**
	 * 初始化聊天
	 */
	const initChat = () => {
		if (!currentSessionId.value) {
			currentSessionId.value = ChatUtils.generateSessionId();
		}
		
		// 加载本地历史记录
		const localHistory = ChatUtils.loadChatHistory(currentSessionId.value);
		if (localHistory.length > 0) {
			messages.value = localHistory;
		} else {
			addWelcomeMessage();
		}
	};

	/**
	 * 添加欢迎消息
	 */
	const addWelcomeMessage = () => {
		const welcomeMessage: ChatMessage = {
			id: ChatUtils.generateMessageId('assistant'),
			content: '您好！我是小虫博士🤖，您的专业害虫检测助手。我可以帮助您：\n\n• 🔍 了解系统功能使用方法\n• 🌾 解答农作物病虫害问题\n• 📊 解释检测结果含义\n• 💡 提供防治建议\n\n有什么问题尽管问我吧！',
			role: 'assistant',
			username: '小虫博士',
			timestamp: new Date().toISOString(),
			status: 'sent',
			sessionId: currentSessionId.value
		};
		
		messages.value.push(welcomeMessage);
		saveChatHistory();
	};

	/**
	 * 发送消息
	 */
	const sendMessage = async (content: string, username: string = '用户'): Promise<boolean> => {
		if (!content.trim() || isLoading.value) {
			return false;
		}

		// 保存当前会话用户
		currentSessionUser.value = username;

		// 检查敏感内容
		if (ChatUtils.checkSensitiveContent(content)) {
			ElMessage.warning('消息包含敏感内容，请重新输入');
			return false;
		}

		// 限制消息长度
		const limitedContent = ChatUtils.limitMessageLength(content);
		if (limitedContent !== content) {
			ElMessage.warning('消息过长，已自动截取');
		}

		// 创建用户消息
		const userMessage: ChatMessage = {
			id: ChatUtils.generateMessageId('user'),
			content: limitedContent,
			role: 'user',
			username,
			timestamp: new Date().toISOString(),
			status: 'sent',
			sessionId: currentSessionId.value
		};

		// 添加到消息列表
		messages.value.push(userMessage);
		saveChatHistory();

		// 设置加载状态
		isLoading.value = true;
		isTyping.value = true;

		let response: any = null;

		try {
			// 构建请求
			const request: ChatRequest = {
				message: limitedContent,
				username,
				sessionId: currentSessionId.value,
				temperature: 0.6
			};

			// 先调用普通API获取完整回复，然后模拟流式效果
			response = await ChatApi.sendMessage(request);
			console.log('ChatStore收到响应:', response);

			if (response.success) {
				// 更新会话ID
				currentSessionId.value = response.sessionId;

				// 创建AI消息占位符
				const aiMessage: ChatMessage = {
					id: response.aiMessage.id,
					content: '',
					role: 'assistant',
					username: '小虫博士',
					timestamp: response.aiMessage.timestamp,
					status: 'sending',
					sessionId: currentSessionId.value,
					recordOperationResult: response.recordOperationResult,
					messageType: response.responseType || 'normal'
				};

				// 添加AI消息到列表
				messages.value.push(aiMessage);
				const aiMessageIndex = messages.value.length - 1;

				// 模拟流式输出效果
				const fullContent = response.aiMessage.content;
				let currentIndex = 0;

				const typeWriter = () => {
					if (currentIndex < fullContent.length && messages.value[aiMessageIndex]) {
						// 每次添加1-3个字符，模拟真实的打字效果
						const charsToAdd = Math.min(Math.floor(Math.random() * 3) + 1, fullContent.length - currentIndex);
						currentIndex += charsToAdd;

						messages.value[aiMessageIndex].content = fullContent.substring(0, currentIndex);

						// 随机延迟，模拟真实的打字速度
						const delay = Math.random() * 100 + 30; // 30-130ms
						setTimeout(typeWriter, delay);
					} else {
						// 完成打字效果
						if (messages.value[aiMessageIndex]) {
							messages.value[aiMessageIndex].status = 'sent';
						}
						saveChatHistory();

						// 如果窗口最小化或关闭，增加未读计数
						if (isMinimized.value || !isOpen.value) {
							unreadCount.value++;
						}

						isLoading.value = false;
						isTyping.value = false;
					}
				};

				// 开始打字效果
				setTimeout(typeWriter, 200); // 延迟200ms开始

				return true;
			} else {
				ElMessage.error(response.error || '发送消息失败');
				return false;
			}
		} catch (error) {
			console.error('发送消息失败:', error);
			ElMessage.error('网络错误，请稍后重试');
			return false;
		} finally {
			// 如果没有开始打字效果，需要重置加载状态
			if (!response || !response.success) {
				isLoading.value = false;
				isTyping.value = false;
			}
		}
	};

	/**
	 * 打开聊天窗口
	 */
	const openChat = () => {
		isOpen.value = true;
		isMinimized.value = false;
		unreadCount.value = 0;

		// 如果没有消息，初始化聊天
		if (messages.value.length === 0) {
			initChat();
		}
	};

	/**
	 * 关闭聊天窗口
	 */
	const closeChat = () => {
		isOpen.value = false;
		isMinimized.value = false;
		saveChatHistory();
	};

	/**
	 * 切换最小化状态
	 */
	const toggleMinimize = () => {
		isMinimized.value = !isMinimized.value;
		if (!isMinimized.value) {
			unreadCount.value = 0;
		}
	};

	/**
	 * 清空聊天历史
	 */
	const clearHistory = async (): Promise<boolean> => {
		try {
			// 清空本地历史
			ChatUtils.clearLocalChatHistory(currentSessionId.value);
			
			// 清空服务器历史
			await ChatApi.clearHistory(currentSessionId.value);
			
			// 重置状态
			messages.value = [];
			currentSessionId.value = ChatUtils.generateSessionId();
			
			// 添加欢迎消息
			addWelcomeMessage();
			
			ElMessage.success('聊天历史已清空');
			return true;
		} catch (error) {
			console.error('清空历史失败:', error);
			ElMessage.error('清空历史失败');
			return false;
		}
	};

	/**
	 * 保存聊天历史到本地
	 */
	const saveChatHistory = () => {
		ChatUtils.saveChatHistory(currentSessionId.value, messages.value);
	};

	/**
	 * 加载聊天历史
	 */
	const loadHistory = async (): Promise<boolean> => {
		try {
			const history = await ChatApi.getHistory(currentSessionId.value);
			messages.value = history;
			saveChatHistory();
			return true;
		} catch (error) {
			console.error('加载历史失败:', error);
			// 尝试加载本地历史
			const localHistory = ChatUtils.loadChatHistory(currentSessionId.value);
			if (localHistory.length > 0) {
				messages.value = localHistory;
				return true;
			}
			return false;
		}
	};

	/**
	 * 设置聊天窗口位置
	 */
	const setPosition = (x: number, y: number) => {
		position.value = { x, y };
	};

	/**
	 * 检查服务健康状态
	 */
	const checkHealth = async (): Promise<boolean> => {
		try {
			await ChatApi.health();
			isOnline.value = true;
			return true;
		} catch (error) {
			console.error('聊天服务不可用:', error);
			isOnline.value = false;
			return false;
		}
	};

	/**
	 * 重试发送消息
	 */
	const retryMessage = async (messageId: string): Promise<boolean> => {
		const message = messages.value.find(msg => msg.id === messageId);
		if (!message || message.role !== 'user') {
			return false;
		}

		// 移除原消息
		const index = messages.value.findIndex(msg => msg.id === messageId);
		if (index > -1) {
			messages.value.splice(index, 1);
		}

		// 重新发送
		return await sendMessage(message.content, message.username);
	};

	/**
	 * 获取消息统计
	 */
	const getMessageStats = () => {
		return {
			total: messages.value.length,
			userMessages: userMessages.value.length,
			aiMessages: aiMessages.value.length,
			sessionId: currentSessionId.value,
			isOnline: isOnline.value
		};
	};

	return {
		// 状态
		isOpen,
		isMinimized,
		isLoading,
		isTyping,
		isOnline,
		unreadCount,
		currentSessionId,
		currentSessionUser,
		messages,
		position,

		// 计算属性
		hasMessages,
		lastMessage,
		userMessages,
		aiMessages,

		// 方法
		initChat,
		sendMessage,
		openChat,
		closeChat,
		toggleMinimize,
		clearHistory,
		loadHistory,
		setPosition,
		checkHealth,
		retryMessage,
		getMessageStats,
		saveChatHistory
	};
});
