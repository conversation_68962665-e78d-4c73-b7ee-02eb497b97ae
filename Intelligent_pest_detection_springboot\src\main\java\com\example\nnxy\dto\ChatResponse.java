package com.example.nnxy.dto;

import com.example.nnxy.entity.ChatMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 聊天响应DTO
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChatResponse {
    
    /**
     * AI回复消息
     */
    private ChatMessage aiMessage;
    
    /**
     * 用户消息
     */
    private ChatMessage userMessage;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 错误信息
     */
    private String error;
    
    /**
     * 聊天历史（可选）
     */
    private List<ChatMessage> history;

    /**
     * 记录操作结果（当涉及记录管理操作时）
     */
    private RecordOperationResult recordOperationResult;

    /**
     * 响应类型：normal(普通聊天)、record_operation(记录操作)
     */
    private String responseType = "normal";

    /**
     * 分析报告数据（当生成报告时）
     */
    private AnalysisReportDTO reportData;
}
