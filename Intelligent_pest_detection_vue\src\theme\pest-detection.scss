// YOLOv11智能害虫检测助手 - 主题样式
// 高级色调配色方案

:root {
  // 主色调 - 深蓝绿色系
  --primary-color: #1e3a8a;
  --primary-light: #3b82f6;
  --primary-dark: #1e40af;
  --primary-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  
  // 辅助色 - 科技感绿色
  --secondary-color: #059669;
  --secondary-light: #10b981;
  --secondary-dark: #047857;
  --secondary-gradient: linear-gradient(135deg, #059669 0%, #10b981 100%);
  
  // 背景色系
  --bg-primary: #f8fafc;
  --bg-secondary: #f1f5f9;
  --bg-card: #ffffff;
  --bg-hover: #f3f4f6;
  --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  
  // 文字色系
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --text-white: #ffffff;
  
  // 边框色系
  --border-light: #e2e8f0;
  --border-medium: #cbd5e1;
  --border-dark: #94a3b8;
  
  // 阴影
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-card: 0 4px 12px rgba(30, 58, 138, 0.08);
  
  // 圆角
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  // 动画
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

// 全局卡片样式
.pest-card {
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-card);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity var(--transition-normal);
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
    
    &::before {
      opacity: 1;
    }
  }
}

// 主要卡片样式
.pest-card-primary {
  @extend .pest-card;
  border-color: var(--primary-light);
  
  &::before {
    opacity: 1;
  }
}

// 检测结果卡片
.pest-card-result {
  @extend .pest-card;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-color: var(--secondary-light);
  
  &::before {
    background: var(--secondary-gradient);
    opacity: 1;
  }
}

// 统计卡片
.pest-card-stats {
  @extend .pest-card;
  text-align: center;
  padding: var(--spacing-lg);
  
  .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
  }
  
  .stats-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
  }
}

// 按钮样式增强
.pest-btn-primary {
  background: var(--primary-gradient);
  border: none;
  color: var(--text-white);
  font-weight: 600;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    
    &::before {
      left: 100%;
    }
  }
  
  &:active {
    transform: translateY(0);
  }
}

.pest-btn-secondary {
  background: var(--secondary-gradient);
  border: none;
  color: var(--text-white);
  font-weight: 600;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
}

// 输入框样式增强
.pest-input {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  transition: all var(--transition-normal);
  background: var(--bg-card);
  
  &:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
  }
  
  &:hover {
    border-color: var(--border-medium);
  }
}

// 页面容器样式
.pest-page-container {
  min-height: 100vh;
  background: var(--bg-gradient);
  padding: var(--spacing-lg);
}

.pest-content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

// 标题样式
.pest-title {
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
  }
}

.pest-subtitle {
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: var(--spacing-md);
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 动画类
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

// 延迟动画
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }

// 响应式设计
@media (max-width: 768px) {
  .pest-content-wrapper {
    padding: var(--spacing-md);
  }
  
  .pest-card {
    margin-bottom: var(--spacing-md);
  }
}
