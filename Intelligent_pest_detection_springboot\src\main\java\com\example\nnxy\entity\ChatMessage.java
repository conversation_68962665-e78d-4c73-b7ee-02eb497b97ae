package com.example.nnxy.entity;

import com.example.nnxy.dto.RecordOperationResult;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 聊天消息实体类
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChatMessage {
    
    /**
     * 消息ID
     */
    private String id;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息角色：user(用户) 或 assistant(AI助手)
     */
    private String role;
    
    /**
     * 发送用户名
     */
    private String username;
    
    /**
     * 消息时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    /**
     * 消息状态：sending(发送中)、sent(已发送)、error(错误)
     */
    private String status;
    
    /**
     * 会话ID，用于区分不同的聊天会话
     */
    private String sessionId;

    /**
     * 记录操作结果（当消息涉及记录管理操作时）
     */
    private RecordOperationResult recordOperationResult;

    /**
     * 消息类型：normal(普通聊天)、record_operation(记录操作)
     */
    private String messageType = "normal";
}
