<template>
	<div class="pest-page-container">
		<div class="pest-content-wrapper">
			<!-- 页面标题 -->
			<div class="page-header animate-fade-in-up">
				<h1 class="pest-title">用户管理</h1>
				<p class="pest-subtitle">管理系统用户账号和权限</p>
			</div>

			<!-- 搜索和操作面板 -->
			<div class="search-panel pest-card animate-fade-in-up animate-delay-100">
				<div class="search-header">
					<i class="iconfont icon-yonghu"></i>
					<span>用户管理</span>
				</div>
				<div class="search-content">
					<div class="search-row">
						<div class="search-item">
							<label class="search-label">用户名</label>
							<el-input
								v-model="state.tableData.param.search"
								placeholder="请输入用户名"
								size="large"
								class="search-input"
								clearable
							>
								<template #prefix>
									<i class="iconfont icon-yonghu"></i>
								</template>
							</el-input>
						</div>
						<div class="search-actions">
							<el-button
								type="primary"
								@click="getTableData()"
								class="pest-btn-primary"
								size="large"
							>
								<i class="iconfont icon-sousuo" style="margin-right: 8px;"></i>
								查询
							</el-button>
							<el-button
								type="success"
								@click="onOpenAddRole('add')"
								class="pest-btn-secondary"
								size="large"
							>
								<i class="iconfont icon-tianjia" style="margin-right: 8px;"></i>
								添加用户
							</el-button>
						</div>
					</div>
				</div>
			</div>

			<!-- 统计信息 -->
			<div class="stats-panel animate-fade-in-up animate-delay-200">
				<div class="stat-card pest-card-stats">
					<div class="stats-number">{{ state.tableData.total }}</div>
					<div class="stats-label">总用户数</div>
				</div>
				<div class="stat-card pest-card-stats">
					<div class="stats-number">{{ state.tableData.data.filter(u => u.role === '管理员').length }}</div>
					<div class="stats-label">管理员</div>
				</div>
				<div class="stat-card pest-card-stats">
					<div class="stats-number">{{ state.tableData.data.filter(u => u.role === '普通用户').length }}</div>
					<div class="stats-label">普通用户</div>
				</div>
				<div class="stat-card pest-card-stats">
					<div class="stats-number">{{ state.tableData.data.length }}</div>
					<div class="stats-label">当前页用户</div>
				</div>
			</div>

			<!-- 用户列表 -->
			<div class="users-section animate-fade-in-up animate-delay-300">
				<div class="users-header">
					<div class="header-info">
						<i class="iconfont icon-liebiao"></i>
						<span>用户列表</span>
					</div>
					<div class="header-actions">
						<el-button
							type="info"
							@click="getTableData()"
							size="default"
						>
							<i class="iconfont icon-shuaxin" style="margin-right: 4px;"></i>
							刷新
						</el-button>
					</div>
				</div>

				<div class="users-grid" v-loading="state.tableData.loading">
					<div
						v-for="(user, index) in state.tableData.data"
						:key="user.id"
						class="user-card pest-card animate-fade-in-up"
						:style="{ animationDelay: `${0.1 * index}s` }"
					>
						<div class="user-header">
							<div class="user-avatar">
								<img :src="user.avatar" :alt="user.name" class="avatar-image" />
							</div>
							<div class="user-basic">
								<h3 class="user-name">{{ user.name }}</h3>
								<p class="user-username">@{{ user.username }}</p>
								<div class="user-role" :class="getRoleClass(user.role)">
									<i :class="getRoleIcon(user.role)"></i>
									<span>{{ user.role }}</span>
								</div>
							</div>
							<div class="user-actions">
								<el-button
									type="primary"
									size="small"
									text
									@click="onOpenEditRole('edit', user)"
								>
									<i class="iconfont icon-bianji"></i>
								</el-button>
								<el-button
									type="danger"
									size="small"
									text
									@click="onRowDel(user)"
								>
									<i class="iconfont icon-shanchu"></i>
								</el-button>
							</div>
						</div>

						<div class="user-details">
							<div class="detail-row">
								<div class="detail-item">
									<i class="iconfont icon-xingbie"></i>
									<span class="detail-label">性别</span>
									<span class="detail-value">{{ user.sex }}</span>
								</div>
								<div class="detail-item">
									<i class="iconfont icon-youxiang"></i>
									<span class="detail-label">邮箱</span>
									<span class="detail-value">{{ user.email }}</span>
								</div>
							</div>
							<div class="detail-row">
								<div class="detail-item">
									<i class="iconfont icon-dianhua"></i>
									<span class="detail-label">手机</span>
									<span class="detail-value">{{ user.tel }}</span>
								</div>
								<div class="detail-item">
									<i class="iconfont icon-bianhao"></i>
									<span class="detail-label">序号</span>
									<span class="detail-value">#{{ user.num }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 分页 -->
				<div class="pagination-section">
					<el-pagination
						@size-change="onHandleSizeChange"
						@current-change="onHandleCurrentChange"
						:pager-count="5"
						:page-sizes="[10, 20, 30, 50]"
						v-model:current-page="state.tableData.param.pageNum"
						background
						v-model:page-size="state.tableData.param.pageSize"
						layout="total, sizes, prev, pager, next, jumper"
						:total="state.tableData.total"
						class="pest-pagination"
					/>
				</div>
			</div>
		</div>

		<RoleDialog ref="roleDialogRef" @refresh="getTableData()" />
	</div>
</template>

<script setup lang="ts" name="systemRole">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import request from '/@/utils/request';

// 引入组件
const RoleDialog = defineAsyncComponent(() => import('./dialog.vue'));

// 定义变量内容
const roleDialogRef = ref();
const state = reactive<SysRoleState>({
	tableData: {
		data: [] as any,
		total: 0,
		loading: false,
		param: {
			search: '',
			pageNum: 1,
			pageSize: 10,
		},
	},
});

const getTableData = () => {
	state.tableData.loading = true;
	request
		.get('/api/user', {
			params: state.tableData.param,
		})
		.then((res) => {
			if (res.code == 0) {
				state.tableData.data = [];
				setTimeout(() => {
					state.tableData.loading = false;
				}, 500);
				for (let i = 0; i < res.data.records.length; i++) {
					state.tableData.data[i] = res.data.records[i];
					state.tableData.data[i]['num'] = i + 1;
					if (state.tableData.data[i]['role'] == 'admin') {
						state.tableData.data[i]['role'] = '管理员';
					} else if (state.tableData.data[i]['role'] == 'common') {
						state.tableData.data[i]['role'] = '普通用户';
					} else if (state.tableData.data[i]['role'] == 'others') {
						state.tableData.data[i]['role'] = '其他用户';
					}
				}
				state.tableData.total = res.data.total;
			} else {
				ElMessage({
					type: 'error',
					message: res.msg,
				});
			}
		});
};

// 打开新增角色弹窗
const onOpenAddRole = (type: string) => {
	roleDialogRef.value.openDialog(type);
};
// 打开修改角色弹窗
const onOpenEditRole = (type: string, row: Object) => {
	roleDialogRef.value.openDialog(type, row);
};

// 删除角色
const onRowDel = (row: any) => {
	ElMessageBox.confirm(`此操作将永久删除该信息，是否继续?`, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			console.log(row);
			request.delete('/api/user/' + row.id).then((res) => {
				if (res.code == 0) {
					console.log(res.data);
					ElMessage({
						type: 'success',
						message: '删除成功！',
					});
				} else {
					ElMessage({
						type: 'error',
						message: res.msg,
					});
				}
			});
			setTimeout(() => {
				getTableData();
			}, 500);
		})
		.catch(() => {});
};
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.param.pageSize = val;
	getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
	state.tableData.param.pageNum = val;
	getTableData();
};

// 获取角色样式类
const getRoleClass = (role: string) => {
	switch (role) {
		case '管理员':
			return 'role-admin';
		case '普通用户':
			return 'role-common';
		case '其他用户':
			return 'role-others';
		default:
			return 'role-default';
	}
};

// 获取角色图标
const getRoleIcon = (role: string) => {
	switch (role) {
		case '管理员':
			return 'iconfont icon-guanliyuan';
		case '普通用户':
			return 'iconfont icon-yonghu';
		case '其他用户':
			return 'iconfont icon-qita';
		default:
			return 'iconfont icon-yonghu';
	}
};

// 页面加载时
onMounted(() => {
	getTableData();
});
</script>

<style scoped lang="scss">
.page-header {
	text-align: center;
	margin-bottom: var(--spacing-xl);
}

.search-panel {
	margin-bottom: var(--spacing-xl);

	.search-header {
		display: flex;
		align-items: center;
		gap: var(--spacing-sm);
		margin-bottom: var(--spacing-lg);
		font-size: 1.125rem;
		font-weight: 600;
		color: var(--text-primary);

		i {
			color: var(--primary-color);
			font-size: 1.25rem;
		}
	}

	.search-content {
		.search-row {
			display: grid;
			grid-template-columns: 1fr auto;
			gap: var(--spacing-lg);
			align-items: end;
		}

		.search-item {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-sm);

			.search-label {
				font-size: 0.875rem;
				font-weight: 500;
				color: var(--text-secondary);
			}

			.search-input {
				:deep(.el-input__wrapper) {
					border: 2px solid var(--border-light);
					border-radius: var(--radius-md);
					transition: all var(--transition-normal);

					&:hover {
						border-color: var(--border-medium);
					}

					&.is-focus {
						border-color: var(--primary-light);
						box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
					}
				}
			}
		}

		.search-actions {
			display: flex;
			gap: var(--spacing-md);

			.el-button {
				height: 48px;
				padding: 0 var(--spacing-xl);
				font-weight: 600;
			}
		}
	}
}

.stats-panel {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: var(--spacing-lg);
	margin-bottom: var(--spacing-xl);
}

.users-section {
	.users-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: var(--spacing-lg);

		.header-info {
			display: flex;
			align-items: center;
			gap: var(--spacing-sm);
			font-size: 1.125rem;
			font-weight: 600;
			color: var(--text-primary);

			i {
				color: var(--primary-color);
				font-size: 1.25rem;
			}
		}
	}

	.users-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
		gap: var(--spacing-lg);
		margin-bottom: var(--spacing-xl);
	}
}

.user-card {
	.user-header {
		display: flex;
		align-items: flex-start;
		gap: var(--spacing-md);
		margin-bottom: var(--spacing-lg);

		.user-avatar {
			flex-shrink: 0;

			.avatar-image {
				width: 60px;
				height: 60px;
				border-radius: 50%;
				object-fit: cover;
				border: 3px solid var(--border-light);
			}
		}

		.user-basic {
			flex: 1;

			.user-name {
				font-size: 1.125rem;
				font-weight: 600;
				color: var(--text-primary);
				margin-bottom: var(--spacing-xs);
			}

			.user-username {
				font-size: 0.875rem;
				color: var(--text-muted);
				margin-bottom: var(--spacing-sm);
			}

			.user-role {
				display: inline-flex;
				align-items: center;
				gap: var(--spacing-xs);
				padding: var(--spacing-xs) var(--spacing-sm);
				border-radius: var(--radius-sm);
				font-size: 0.75rem;
				font-weight: 500;

				&.role-admin {
					background: rgba(220, 38, 38, 0.1);
					color: #dc2626;
				}

				&.role-common {
					background: rgba(59, 130, 246, 0.1);
					color: #3b82f6;
				}

				&.role-others {
					background: rgba(107, 114, 128, 0.1);
					color: #6b7280;
				}
			}
		}

		.user-actions {
			display: flex;
			gap: var(--spacing-xs);

			.el-button {
				width: 32px;
				height: 32px;
				padding: 0;

				&:hover {
					transform: scale(1.1);
				}
			}
		}
	}

	.user-details {
		.detail-row {
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: var(--spacing-md);
			margin-bottom: var(--spacing-sm);

			&:last-child {
				margin-bottom: 0;
			}
		}

		.detail-item {
			display: flex;
			align-items: center;
			gap: var(--spacing-xs);

			i {
				color: var(--primary-color);
				font-size: 0.875rem;
				width: 16px;
			}

			.detail-label {
				font-size: 0.75rem;
				color: var(--text-muted);
				font-weight: 500;
			}

			.detail-value {
				font-size: 0.875rem;
				color: var(--text-primary);
				font-weight: 500;
				margin-left: auto;
			}
		}
	}
}

.pagination-section {
	display: flex;
	justify-content: center;
	margin-top: var(--spacing-xl);

	.pest-pagination {
		:deep(.el-pagination) {
			.el-pager li {
				border-radius: var(--radius-sm);
				margin: 0 2px;

				&.is-active {
					background: var(--primary-gradient);
					color: white;
				}
			}

			.btn-prev, .btn-next {
				border-radius: var(--radius-sm);
			}
		}
	}
}

// 响应式设计
@media (max-width: 1024px) {
	.search-content .search-row {
		grid-template-columns: 1fr;
		gap: var(--spacing-md);
	}

	.stats-panel {
		grid-template-columns: repeat(2, 1fr);
	}

	.users-grid {
		grid-template-columns: 1fr;
	}
}

@media (max-width: 768px) {
	.stats-panel {
		grid-template-columns: 1fr;
	}

	.user-details .detail-row {
		grid-template-columns: 1fr;
	}

	.search-actions {
		flex-direction: column;
	}
}
</style>
