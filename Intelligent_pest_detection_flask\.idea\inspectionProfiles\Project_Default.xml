<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="LanguageDetectionInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyInterpreterInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="11">
            <item index="0" class="java.lang.String" itemvalue="requests" />
            <item index="1" class="java.lang.String" itemvalue="torch" />
            <item index="2" class="java.lang.String" itemvalue="torchvision" />
            <item index="3" class="java.lang.String" itemvalue="sympy" />
            <item index="4" class="java.lang.String" itemvalue="torchaudio" />
            <item index="5" class="java.lang.String" itemvalue="ultralytics" />
            <item index="6" class="java.lang.String" itemvalue="tqdm" />
            <item index="7" class="java.lang.String" itemvalue="matplotlib" />
            <item index="8" class="java.lang.String" itemvalue="wandb" />
            <item index="9" class="java.lang.String" itemvalue="numpy" />
            <item index="10" class="java.lang.String" itemvalue="Pillow" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E262" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N812" />
          <option value="N806" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="cv2.*" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>