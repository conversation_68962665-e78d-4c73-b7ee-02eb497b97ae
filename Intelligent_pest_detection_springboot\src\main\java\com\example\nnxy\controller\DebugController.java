package com.example.nnxy.controller;

import com.example.nnxy.entity.ImgRecords;
import com.example.nnxy.mapper.ImgRecordsMapper;
import com.example.nnxy.dto.RecordQueryRequest;
import com.example.nnxy.dto.RecordOperationResult;
import com.example.nnxy.service.RecordManagementService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调试控制器 - 用于诊断查询问题
 */
@Slf4j
@RestController
@RequestMapping("/api/debug")
@CrossOrigin(origins = "*")
public class DebugController {

    @Resource
    private ImgRecordsMapper imgRecordsMapper;

    @Resource
    private RecordManagementService recordManagementService;

    /**
     * 数据库连接测试
     */
    @GetMapping("/database")
    public Map<String, Object> testDatabase() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询总记录数
            long totalCount = imgRecordsMapper.selectCount(null);
            result.put("totalRecords", totalCount);
            
            // 查询用户nxc的记录数
            LambdaQueryWrapper<ImgRecords> wrapper = Wrappers.<ImgRecords>lambdaQuery()
                .eq(ImgRecords::getUsername, "nxc");
            long nxcCount = imgRecordsMapper.selectCount(wrapper);
            result.put("nxcRecords", nxcCount);
            
            // 查询所有用户
            List<ImgRecords> allRecords = imgRecordsMapper.selectList(null);
            Map<String, Long> userCounts = new HashMap<>();
            allRecords.forEach(record -> {
                String username = record.getUsername();
                userCounts.put(username, userCounts.getOrDefault(username, 0L) + 1);
            });
            result.put("userCounts", userCounts);
            
            // 查询最新的5条记录
            LambdaQueryWrapper<ImgRecords> latestWrapper = Wrappers.<ImgRecords>lambdaQuery()
                .orderByDesc(ImgRecords::getId)
                .last("LIMIT 5");
            List<ImgRecords> latestRecords = imgRecordsMapper.selectList(latestWrapper);
            result.put("latestRecords", latestRecords);
            
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("数据库测试失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 插入测试数据
     */
    @PostMapping("/insert-test-data")
    public Map<String, Object> insertTestData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ImgRecords testRecord = new ImgRecords();
            testRecord.setUsername("nxc");
            testRecord.setKind("corn");
            testRecord.setLabel("玉米疫病");
            testRecord.setConf("0.95");
            testRecord.setStartTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            testRecord.setWeight("corn_best.pt");
            
            int insertResult = imgRecordsMapper.insert(testRecord);
            
            result.put("success", true);
            result.put("insertResult", insertResult);
            result.put("insertedId", testRecord.getId());
            result.put("insertedRecord", testRecord);
            
        } catch (Exception e) {
            log.error("插入测试数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试查询服务
     */
    @PostMapping("/test-query")
    public Map<String, Object> testQuery(@RequestBody(required = false) RecordQueryRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 如果没有提供请求参数，使用默认参数
            if (request == null) {
                request = RecordQueryRequest.builder()
                    .operationType("QUERY")
                    .recordType("IMG")
                    .username("nxc")
                    .onlyCurrentUser(true)
                    .needConfirmation(false)
                    .build();
            }
            
            log.info("测试查询请求: {}", request);
            
            RecordOperationResult queryResult = recordManagementService.executeOperation(request);
            
            result.put("success", true);
            result.put("request", request);
            result.put("queryResult", queryResult);
            
        } catch (Exception e) {
            log.error("查询测试失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试简单查询
     */
    @GetMapping("/simple-query/{username}")
    public Map<String, Object> simpleQuery(@PathVariable String username) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 最简单的查询
            LambdaQueryWrapper<ImgRecords> wrapper = Wrappers.<ImgRecords>lambdaQuery()
                .eq(ImgRecords::getUsername, username);
            
            List<ImgRecords> records = imgRecordsMapper.selectList(wrapper);
            
            result.put("success", true);
            result.put("username", username);
            result.put("recordCount", records.size());
            result.put("records", records);
            
        } catch (Exception e) {
            log.error("简单查询失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试不同查询条件
     */
    @GetMapping("/test-conditions")
    public Map<String, Object> testConditions() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> tests = new HashMap<>();
            
            // 测试1: 查询所有记录
            long allCount = imgRecordsMapper.selectCount(null);
            tests.put("allRecords", allCount);
            
            // 测试2: 查询用户nxc
            LambdaQueryWrapper<ImgRecords> nxcWrapper = Wrappers.<ImgRecords>lambdaQuery()
                .eq(ImgRecords::getUsername, "nxc");
            long nxcCount = imgRecordsMapper.selectCount(nxcWrapper);
            tests.put("nxcRecords", nxcCount);
            
            // 测试3: 查询今天的记录
            String today = LocalDateTime.now().toLocalDate().toString();
            LambdaQueryWrapper<ImgRecords> todayWrapper = Wrappers.<ImgRecords>lambdaQuery()
                .like(ImgRecords::getStartTime, today);
            long todayCount = imgRecordsMapper.selectCount(todayWrapper);
            tests.put("todayRecords", todayCount);
            
            // 测试4: 查询用户nxc今天的记录
            LambdaQueryWrapper<ImgRecords> nxcTodayWrapper = Wrappers.<ImgRecords>lambdaQuery()
                .eq(ImgRecords::getUsername, "nxc")
                .like(ImgRecords::getStartTime, today);
            long nxcTodayCount = imgRecordsMapper.selectCount(nxcTodayWrapper);
            tests.put("nxcTodayRecords", nxcTodayCount);
            
            result.put("success", true);
            result.put("tests", tests);
            
        } catch (Exception e) {
            log.error("条件测试失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试作物类型查询
     */
    @GetMapping("/test-crop-query/{cropType}")
    public Map<String, Object> testCropQuery(@PathVariable String cropType) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 测试按作物类型查询
            LambdaQueryWrapper<ImgRecords> wrapper = Wrappers.<ImgRecords>lambdaQuery()
                .eq(ImgRecords::getKind, cropType);

            List<ImgRecords> records = imgRecordsMapper.selectList(wrapper);

            result.put("success", true);
            result.put("cropType", cropType);
            result.put("recordCount", records.size());
            result.put("records", records);

        } catch (Exception e) {
            log.error("作物类型查询失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 清理测试数据
     */
    @DeleteMapping("/cleanup-test-data")
    public Map<String, Object> cleanupTestData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 删除测试插入的数据（标签包含"测试"的记录）
            LambdaQueryWrapper<ImgRecords> wrapper = Wrappers.<ImgRecords>lambdaQuery()
                .like(ImgRecords::getLabel, "测试")
                .or()
                .like(ImgRecords::getLabel, "玉米疫病");
            
            int deletedCount = imgRecordsMapper.delete(wrapper);
            
            result.put("success", true);
            result.put("deletedCount", deletedCount);
            
        } catch (Exception e) {
            log.error("清理测试数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
