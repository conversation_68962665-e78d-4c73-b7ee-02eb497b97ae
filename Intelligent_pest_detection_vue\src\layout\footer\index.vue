<template>
	<div class="pest-footer">
		<div class="footer-content">
			<!-- 系统信息区域 -->
			<div class="footer-section">
				<div class="footer-logo">
					<img src="/src/assets/Logo.png" alt="YOLO11智能虫害检测系统" class="footer-logo-img" />
					<span class="footer-title">YOLO11智能虫害检测系统</span>
				</div>
				<p class="footer-description">
					基于先进的YOLOv11深度学习算法，为农业生产提供智能化害虫检测解决方案
				</p>
			</div>

			<!-- 功能模块区域 -->
			<div class="footer-section">
				<h4 class="section-title">核心功能</h4>
				<ul class="footer-links">
					<li><router-link to="/imgPredict">图像害虫检测</router-link></li>
					<li><router-link to="/videoPredict">视频害虫检测</router-link></li>
					<li><router-link to="/cameraPredict">实时害虫检测</router-link></li>
					<li><router-link to="/imgRecord">检测记录管理</router-link></li>
				</ul>
			</div>

			<!-- 技术支持区域 -->
			<div class="footer-section">
				<h4 class="section-title">技术支持</h4>
				<ul class="footer-links">
					<li><a href="#" @click.prevent>使用指南</a></li>
					<li><a href="#" @click.prevent>API文档</a></li>
					<li><a href="#" @click.prevent>常见问题</a></li>
					<li><a href="#" @click.prevent>技术支持</a></li>
				</ul>
			</div>

			<!-- 联系信息区域 -->
			<div class="footer-section">
				<h4 class="section-title">联系我们</h4>
				<div class="contact-info">
					<div class="contact-item">
						<i class="iconfont icon-youxiang"></i>
						<span><EMAIL></span>
					</div>
					<div class="contact-item">
						<i class="iconfont icon-dianhua"></i>
						<span>400-123-4567</span>
					</div>
					<div class="contact-item">
						<i class="iconfont icon-dizhi"></i>
						<span>北京市海淀区科技园</span>
					</div>
				</div>
			</div>
		</div>

		<!-- 版权信息 -->
		<div class="footer-bottom">
			<div class="copyright">
				<p>&copy; 2024 YOLO11智能虫害检测系统. All rights reserved.</p>
				<p>Powered by Vue 3 + TypeScript + Element Plus + YOLOv11</p>
			</div>
			<div class="tech-badges">
				<span class="tech-badge">Vue 3</span>
				<span class="tech-badge">TypeScript</span>
				<span class="tech-badge">YOLOv11</span>
				<span class="tech-badge">Element Plus</span>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts" name="layoutFooter">
// YOLOv11智能害虫检测助手页脚组件
</script>

<style scoped lang="scss">
.pest-footer {
	background: var(--bg-card);
	border-top: 1px solid var(--border-light);
	margin-top: auto;

	.footer-content {
		max-width: 1200px;
		margin: 0 auto;
		padding: var(--spacing-2xl) var(--spacing-lg);
		display: grid;
		grid-template-columns: 2fr 1fr 1fr 1fr;
		gap: var(--spacing-xl);
	}

	.footer-section {
		.footer-logo {
			display: flex;
			align-items: center;
			gap: var(--spacing-md);
			margin-bottom: var(--spacing-md);

			.footer-logo-img {
				width: 40px;
				height: 40px;
				object-fit: contain;
			}

			.footer-title {
				font-size: 1.25rem;
				font-weight: 700;
				color: var(--text-primary);
			}
		}

		.footer-description {
			color: var(--text-secondary);
			line-height: 1.6;
			font-size: 0.875rem;
		}

		.section-title {
			color: var(--text-primary);
			font-weight: 600;
			margin-bottom: var(--spacing-md);
			font-size: 1rem;
		}

		.footer-links {
			list-style: none;
			padding: 0;
			margin: 0;

			li {
				margin-bottom: var(--spacing-sm);

				a {
					color: var(--text-secondary);
					text-decoration: none;
					font-size: 0.875rem;
					transition: all var(--transition-normal);

					&:hover {
						color: var(--primary-color);
						transform: translateX(4px);
					}
				}
			}
		}

		.contact-info {
			.contact-item {
				display: flex;
				align-items: center;
				gap: var(--spacing-sm);
				margin-bottom: var(--spacing-sm);
				color: var(--text-secondary);
				font-size: 0.875rem;

				i {
					color: var(--primary-color);
					width: 16px;
				}
			}
		}
	}

	.footer-bottom {
		border-top: 1px solid var(--border-light);
		padding: var(--spacing-lg);
		display: flex;
		justify-content: space-between;
		align-items: center;
		max-width: 1200px;
		margin: 0 auto;

		.copyright {
			color: var(--text-muted);
			font-size: 0.75rem;
			line-height: 1.5;
		}

		.tech-badges {
			display: flex;
			gap: var(--spacing-sm);

			.tech-badge {
				background: var(--primary-gradient);
				color: var(--text-white);
				padding: var(--spacing-xs) var(--spacing-sm);
				border-radius: var(--radius-sm);
				font-size: 0.625rem;
				font-weight: 500;
			}
		}
	}
}

// 响应式设计
@media (max-width: 768px) {
	.pest-footer {
		.footer-content {
			grid-template-columns: 1fr;
			gap: var(--spacing-lg);
			padding: var(--spacing-lg);
		}

		.footer-bottom {
			flex-direction: column;
			gap: var(--spacing-md);
			text-align: center;

			.tech-badges {
				justify-content: center;
			}
		}
	}
}
</style>
