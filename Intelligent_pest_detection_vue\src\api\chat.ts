import request from '/@/utils/request';

/**
 * 2025.6.15 nxc 整理
 * 记录操作结果接口
 */
export interface RecordOperationResult {
	success: boolean;
	operationType: string;
	recordType: string;
	message: string;
	error?: string;
	data?: any;
	totalCount?: number;
	currentPageCount?: number;
	totalPages?: number;
	currentPage?: number;
	affectedCount?: number;
	statistics?: Record<string, any>;
	details?: string[];
	needConfirmation?: boolean;
	confirmationMessage?: string;
	suggestions?: string[];
	executionTime?: number;
}

/**
 * 聊天消息接口
 */
export interface ChatMessage {
	id: string;
	content: string;
	role: 'user' | 'assistant';
	username: string;
	timestamp: string;
	status: 'sending' | 'sent' | 'error';
	sessionId: string;
	recordOperationResult?: RecordOperationResult;
	messageType?: 'normal' | 'record_operation';
}

/**
 * 聊天请求接口
 */
export interface ChatRequest {
	message: string;
	username?: string;
	sessionId?: string;
	stream?: boolean;
	temperature?: number;
}

/**
 * 聊天响应接口
 */
export interface ChatResponse {
	aiMessage: ChatMessage;
	userMessage: ChatMessage;
	sessionId: string;
	success: boolean;
	error?: string;
	history?: ChatMessage[];
	recordOperationResult?: RecordOperationResult;
	responseType?: 'normal' | 'record_operation';
	reportData?: AnalysisReportDTO;
}

/**
 * 分析报告接口
 */
export interface AnalysisReportDTO {
	reportId: string;
	title: string;
	reportType: string;
	generateTime: string;
	queryConditions: string;
	cropType: string;
	timeRange: string;
	totalRecords: number;
	executiveSummary?: ExecutiveSummary;
	diseaseAnalysis?: DiseaseAnalysis;
	confidenceAnalysis?: ConfidenceAnalysis;
	trendAnalysis?: TrendAnalysis;
	recommendations?: string[];
	detailData?: any[];
	chartData?: Record<string, any>;
}

export interface ExecutiveSummary {
	overview: string;
	keyMetrics: Record<string, any>;
	highlights: string[];
}

export interface DiseaseAnalysis {
	diseaseDistribution: Record<string, number>;
	mostCommonDisease: string;
	diseaseRate: number;
	diseaseDetails: DiseaseDetail[];
}

export interface DiseaseDetail {
	diseaseName: string;
	count: number;
	percentage: number;
	avgConfidence: number;
	severity: string;
	description: string;
}

export interface ConfidenceAnalysis {
	averageConfidence: number;
	highConfidenceRate: number;
	mediumConfidenceRate: number;
	lowConfidenceRate: number;
	confidenceByDisease: Record<string, number>;
}

export interface TrendAnalysis {
	timePoints: TimePoint[];
	trend: string;
	seasonalPattern: string;
}

export interface TimePoint {
	date: string;
	count: number;
	diseaseCount: Record<string, number>;
}

/**
 * 聊天API类
 */
export class ChatApi {
	/**
	 * 发送聊天消息
	 */
	static async sendMessage(data: ChatRequest): Promise<ChatResponse> {
		const response = await request.post('/api/chat/send', data);
		console.log('API响应:', response);

		// 处理后端返回的格式 {code: "0", msg: "成功", data: {...}}
		if (response.code === "0" || response.code === 0) {
			return response.data;
		} else {
			throw new Error(response.msg || '发送消息失败');
		}
	}

	/**
	 * 获取会话历史
	 */
	static async getHistory(sessionId: string): Promise<ChatMessage[]> {
		return request.get(`/api/chat/history/${sessionId}`);
	}

	/**
	 * 清除会话历史
	 */
	static async clearHistory(sessionId: string): Promise<string> {
		return request.delete(`/api/chat/history/${sessionId}`);
	}

	/**
	 * 获取所有会话ID
	 */
	static async getAllSessions(): Promise<string[]> {
		return request.get('/api/chat/sessions');
	}

	/**
	 * 流式发送消息
	 */
	static sendStreamMessage(
		data: ChatRequest,
		onMessage: (event: any) => void,
		onError: (error: any) => void,
		onComplete: () => void
	): void {
		// 使用fetch实现流式请求
		fetch('/api/chat/stream', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(data)
		}).then(response => {
			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const reader = response.body?.getReader();
			const decoder = new TextDecoder();
			let buffer = '';

			function readStream(): Promise<void> {
				return reader!.read().then(({ done, value }) => {
					if (done) {
						onComplete();
						return;
					}

					buffer += decoder.decode(value, { stream: true });
					const lines = buffer.split('\n');

					// 保留最后一行（可能不完整）
					buffer = lines.pop() || '';

					for (const line of lines) {
						if (line.trim() === '') continue;

						if (line.startsWith('data: ')) {
							try {
								const eventData = JSON.parse(line.slice(6));
								onMessage({ name: 'delta', data: eventData });
							} catch (e) {
								console.warn('解析SSE数据失败:', line);
							}
						} else if (line.startsWith('event: ')) {
							const eventName = line.slice(7);
							onMessage({ name: eventName, data: null });
						}
					}

					return readStream();
				});
			}

			return readStream();
		}).catch(onError);
	}

	/**
	 * 健康检查
	 */
	static async health(): Promise<string> {
		return request.get('/api/chat/health');
	}

	/**
	 * 解析记录管理指令
	 */
	static async parseCommand(data: ChatRequest): Promise<any> {
		return request.post('/api/chat/parse-command', data);
	}

	/**
	 * 执行记录管理操作
	 */
	static async executeRecordOperation(data: any): Promise<RecordOperationResult> {
		return request.post('/api/chat/execute-record-operation', data);
	}

	/**
	 * 获取记录管理建议指令
	 */
	static async getRecordSuggestions(): Promise<string[]> {
		return request.get('/api/chat/record-suggestions');
	}

	/**
	 * 检查指令是否为记录管理类型
	 */
	static async checkRecordCommand(data: ChatRequest): Promise<boolean> {
		return request.post('/api/chat/check-record-command', data);
	}

	/**
	 * 生成分析报告
	 */
	static async generateReport(data: {
		cropType?: string;
		reportType?: string;
		timeRange?: string;
		customDateRange?: [Date, Date];
		recordTypes?: string[];
		analysisDimensions?: string[];
		confidenceRange?: [number, number];
	}): Promise<AnalysisReportDTO> {
		const response = await request.post('/api/reports/generate', data);
		if (response.code === "0" || response.code === 0) {
			return response.data;
		} else {
			throw new Error(response.msg || '生成报告失败');
		}
	}

	/**
	 * 获取报告历史
	 */
	static async getReportHistory(): Promise<AnalysisReportDTO[]> {
		const response = await request.get('/api/reports/history');
		if (response.code === "0" || response.code === 0) {
			return response.data;
		} else {
			throw new Error(response.msg || '获取报告历史失败');
		}
	}

	/**
	 * 删除报告
	 */
	static async deleteReport(reportId: string): Promise<void> {
		const response = await request.delete(`/api/reports/${reportId}`);
		if (response.code !== "0" && response.code !== 0) {
			throw new Error(response.msg || '删除报告失败');
		}
	}

	/**
	 * 导出报告
	 */
	static async exportReport(reportId: string, format: 'pdf' | 'excel' | 'json' | 'txt'): Promise<Blob> {
		const response = await request.get(`/api/reports/${reportId}/export/${format}`, {
			responseType: 'blob'
		});
		return response;
	}
}

/**
 * 聊天工具类
 */
export class ChatUtils {
	/**
	 * 生成会话ID
	 */
	static generateSessionId(): string {
		return 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
	}

	/**
	 * 生成消息ID
	 */
	static generateMessageId(role: 'user' | 'assistant'): string {
		return role + '-' + Date.now() + '-' + Math.random().toString(36).substr(2, 6);
	}

	/**
	 * 格式化时间
	 */
	static formatTime(timestamp: string): string {
		const date = new Date(timestamp);
		const now = new Date();
		const diff = now.getTime() - date.getTime();
		
		// 小于1分钟显示"刚刚"
		if (diff < 60000) {
			return '刚刚';
		}
		
		// 小于1小时显示"X分钟前"
		if (diff < 3600000) {
			return Math.floor(diff / 60000) + '分钟前';
		}
		
		// 小于24小时显示"X小时前"
		if (diff < 86400000) {
			return Math.floor(diff / 3600000) + '小时前';
		}
		
		// 超过24小时显示具体时间
		if (date.toDateString() === now.toDateString()) {
			return date.toLocaleTimeString('zh-CN', { 
				hour: '2-digit', 
				minute: '2-digit' 
			});
		} else {
			return date.toLocaleDateString('zh-CN', { 
				month: 'short', 
				day: 'numeric',
				hour: '2-digit', 
				minute: '2-digit' 
			});
		}
	}

	/**
	 * 格式化消息内容
	 */
	static formatMessage(content: string): string {
		return content
			.replace(/\n/g, '<br>')
			.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
			.replace(/\*(.*?)\*/g, '<em>$1</em>')
			.replace(/`(.*?)`/g, '<code>$1</code>')
			.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
	}

	/**
	 * 检查消息是否包含敏感内容
	 */
	static checkSensitiveContent(content: string): boolean {
		const sensitiveWords = ['政治', '暴力', '色情', '赌博', '毒品'];
		return sensitiveWords.some(word => content.includes(word));
	}

	/**
	 * 限制消息长度
	 */
	static limitMessageLength(content: string, maxLength: number = 1000): string {
		if (content.length <= maxLength) {
			return content;
		}
		return content.substring(0, maxLength) + '...';
	}

	/**
	 * 获取消息摘要
	 */
	static getMessageSummary(content: string, maxLength: number = 50): string {
		const plainText = content.replace(/<[^>]*>/g, '').replace(/\n/g, ' ');
		return this.limitMessageLength(plainText, maxLength);
	}

	/**
	 * 验证会话ID格式
	 */
	static validateSessionId(sessionId: string): boolean {
		return /^session-\d+-[a-z0-9]+$/.test(sessionId);
	}

	/**
	 * 本地存储聊天历史
	 */
	static saveChatHistory(sessionId: string, messages: ChatMessage[]): void {
		try {
			const key = `chat_history_${sessionId}`;
			localStorage.setItem(key, JSON.stringify(messages));
		} catch (error) {
			console.warn('保存聊天历史失败:', error);
		}
	}

	/**
	 * 加载本地聊天历史
	 */
	static loadChatHistory(sessionId: string): ChatMessage[] {
		try {
			const key = `chat_history_${sessionId}`;
			const data = localStorage.getItem(key);
			return data ? JSON.parse(data) : [];
		} catch (error) {
			console.warn('加载聊天历史失败:', error);
			return [];
		}
	}

	/**
	 * 清除本地聊天历史
	 */
	static clearLocalChatHistory(sessionId?: string): void {
		try {
			if (sessionId) {
				const key = `chat_history_${sessionId}`;
				localStorage.removeItem(key);
			} else {
				// 清除所有聊天历史
				const keys = Object.keys(localStorage).filter(key => key.startsWith('chat_history_'));
				keys.forEach(key => localStorage.removeItem(key));
			}
		} catch (error) {
			console.warn('清除聊天历史失败:', error);
		}
	}
}

/**
 * 聊天状态管理
 */
export class ChatState {
	private static instance: ChatState;
	private listeners: Map<string, Function[]> = new Map();

	static getInstance(): ChatState {
		if (!ChatState.instance) {
			ChatState.instance = new ChatState();
		}
		return ChatState.instance;
	}

	/**
	 * 添加事件监听器
	 */
	on(event: string, callback: Function): void {
		if (!this.listeners.has(event)) {
			this.listeners.set(event, []);
		}
		this.listeners.get(event)!.push(callback);
	}

	/**
	 * 移除事件监听器
	 */
	off(event: string, callback: Function): void {
		const callbacks = this.listeners.get(event);
		if (callbacks) {
			const index = callbacks.indexOf(callback);
			if (index > -1) {
				callbacks.splice(index, 1);
			}
		}
	}

	/**
	 * 触发事件
	 */
	emit(event: string, ...args: any[]): void {
		const callbacks = this.listeners.get(event);
		if (callbacks) {
			callbacks.forEach(callback => callback(...args));
		}
	}
}

// 便捷导出
export const chatApi = ChatApi;
export const chatUtils = ChatUtils;
export const chatState = ChatState.getInstance();

export default ChatApi;
