spring.application.name=Nnxy
mybatis.mapper-locations=classpath:mappers/*xml
mybatis.type-aliases-package=com.example.nnxy.mybatis.entity
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.name=defaultDataSource
spring.datasource.url=********************************************************************
spring.datasource.username=root
spring.datasource.password=123456
server.port=9999
file.ip=localhost
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB

# DeepSeek-R1 AI 配置
deepseek.api.key=l4JMARQ5dI6nZ1UEAeWw_Ovr35K9qjhxCKvazbkQlY_Z6UGUixSoVI6IimTznyG2FikGU1-TJL-ChG98XF2I6Q
deepseek.api.url=https://maas-cn-southwest-2.modelarts-maas.com/v1/infers/8a062fd4-7367-4ab4-a936-5eeb8fb821c4/v1/chat/completions
deepseek.model=DeepSeek-R1
deepseek.temperature=0.6




