{"name": "Intelligent_pest_detection_vue", "version": "2.4.21", "description": "YOLOv11智能害虫检测助手", "author": "nxc_2025608", "license": "MIT", "scripts": {"dev": "vite --force", "build": "vite build", "lint-fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "axios": "^1.2.1", "countup.js": "^2.3.2", "cropperjs": "^1.5.13", "echarts": "^5.4.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.2.26", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-cookie": "^3.0.1", "js-table2excel": "^1.0.3", "jspdf": "^3.0.1", "jsplumb": "^2.15.6", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.28", "print-js": "^1.6.0", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.11.0", "screenfull": "^6.0.2", "socket.io-client": "^4.8.1", "sortablejs": "^1.15.0", "splitpanes": "^3.1.5", "vue": "^3.2.45", "vue-clipboard3": "^2.0.0", "vue-grid-layout": "^3.0.0-beta1", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vue3-seamless-scroll": "^2.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^18.11.13", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.46.0", "@typescript-eslint/parser": "^5.46.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/compiler-sfc": "^3.2.45", "eslint": "^8.29.0", "eslint-plugin-vue": "^9.8.0", "prettier": "^2.8.1", "sass": "^1.56.2", "typescript": "^4.9.4", "vite": "^4.0.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.1.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://gitee.com/nxc/vue-next-admin/issues"}, "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus", "linfeng", "next-admin"], "repository": {"type": "git", "url": "https://gitee.com/nxc/vue-next-admin.git"}}