<template>
	<div class="pest-page-container">
		<div class="pest-content-wrapper">
			<!-- 页面标题 -->
			<div class="page-header animate-fade-in-up">
				<h1 class="pest-title">实时害虫检测</h1>
				<p class="pest-subtitle">使用摄像头实时检测农作物害虫，支持实时录制和分析</p>
			</div>

			<!-- 控制面板 -->
			<div class="control-panel pest-card animate-fade-in-up animate-delay-100">
				<div class="control-header">
					<i class="iconfont icon-shezhi"></i>
					<span>检测参数设置</span>
				</div>

				<div class="control-content">
					<div class="control-row">
						<div class="control-item">
							<label class="control-label">作物种类</label>
							<el-select
								v-model="kind"
								placeholder="请选择作物种类"
								size="large"
								class="control-select"
								@change="getData"
							>
								<el-option
									v-for="item in state.kind_items"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>

						<div class="control-item">
							<label class="control-label">检测模型</label>
							<el-select
								v-model="weight"
								placeholder="请选择模型"
								size="large"
								class="control-select"
							>
								<el-option
									v-for="item in state.weight_items"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>

						<div class="control-item confidence-item">
							<label class="control-label">置信度阈值</label>
							<div class="confidence-slider">
								<el-slider
									v-model="conf"
									:format-tooltip="formatTooltip"
									:show-tooltip="true"
									class="slider"
								/>
								<span class="confidence-value">{{ formatTooltip(conf) }}</span>
							</div>
						</div>
					</div>

					<div class="action-row">
						<el-button
							type="primary"
							@click="start"
							class="pest-btn-primary start-button"
							size="large"
							:disabled="!weight || !kind || state.cameraisShow"
						>
							<i class="iconfont icon-kaishi" style="margin-right: 8px;"></i>
							开始检测
						</el-button>

						<el-button
							type="danger"
							@click="stop"
							class="stop-button"
							size="large"
							:disabled="!state.cameraisShow"
						>
							<i class="iconfont icon-tingzhi" style="margin-right: 8px;"></i>
							停止检测
						</el-button>
					</div>

					<!-- 进度条 -->
					<div class="progress-section" v-if="state.isShow">
						<div class="progress-info">
							<i class="iconfont icon-jiazai"></i>
							<span>{{ state.type_text }}</span>
						</div>
						<el-progress
							:text-inside="true"
							:stroke-width="24"
							:percentage="state.percentage"
							class="progress-bar"
						>
							<span>{{ state.percentage }}%</span>
						</el-progress>
					</div>
				</div>
			</div>

			<!-- 实时检测显示区域 -->
			<div class="camera-display pest-card animate-fade-in-up animate-delay-200">
				<div class="camera-header">
					<i class="iconfont icon-shexiangtou1"></i>
					<span>实时检测画面</span>
					<div class="status-indicator" :class="{ active: state.cameraisShow }">
						<span class="status-dot"></span>
						<span class="status-text">{{ state.cameraisShow ? '检测中' : '未启动' }}</span>
					</div>
				</div>

				<div class="camera-container">
					<div v-if="state.cameraisShow" class="camera-view">
						<img :src="state.video_path" class="camera-stream" />
						<div class="camera-overlay">
							<div class="recording-indicator">
								<i class="iconfont icon-luxiang"></i>
								<span>实时检测中...</span>
							</div>
						</div>
					</div>
					<div v-else class="camera-placeholder">
						<i class="iconfont icon-shexiangtou1"></i>
						<h3>摄像头未启动</h3>
						<p>点击"开始检测"按钮启动实时害虫检测</p>
						<div class="placeholder-features">
							<div class="feature-tag">
								<i class="iconfont icon-shishi"></i>
								<span>实时检测</span>
							</div>
							<div class="feature-tag">
								<i class="iconfont icon-gaojing"></i>
								<span>智能预警</span>
							</div>
							<div class="feature-tag">
								<i class="iconfont icon-baocun"></i>
								<span>自动录制</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 实时检测特色 -->
			<div class="realtime-features pest-card animate-fade-in-up animate-delay-300">
				<div class="features-header">
					<i class="iconfont icon-tese"></i>
					<span>实时检测优势</span>
				</div>
				<div class="features-grid">
					<div class="feature-item">
						<i class="iconfont icon-shishi"></i>
						<h4>实时监控</h4>
						<p>24小时不间断监控，及时发现害虫活动</p>
					</div>
					<div class="feature-item">
						<i class="iconfont icon-kuaisu"></i>
						<h4>快速响应</h4>
						<p>毫秒级检测响应，第一时间发现问题</p>
					</div>
					<div class="feature-item">
						<i class="iconfont icon-zidong"></i>
						<h4>自动记录</h4>
						<p>自动保存检测过程和结果，便于后续分析</p>
					</div>
					<div class="feature-item">
						<i class="iconfont icon-gaojing"></i>
						<h4>智能预警</h4>
						<p>发现害虫时自动预警，支持多种通知方式</p>
					</div>
				</div>
			</div>

			<!-- 使用提示 -->
			<div class="usage-tips pest-card animate-fade-in-up animate-delay-400">
				<div class="tips-header">
					<i class="iconfont icon-bangzhu"></i>
					<span>使用提示</span>
				</div>
				<div class="tips-content">
					<div class="tip-section">
						<h4>设备要求</h4>
						<ul>
							<li>确保摄像头正常连接并授权使用</li>
							<li>建议使用高清摄像头以获得更好的检测效果</li>
							<li>保证充足的光照条件</li>
						</ul>
					</div>
					<div class="tip-section">
						<h4>检测建议</h4>
						<ul>
							<li>将摄像头对准需要监控的作物区域</li>
							<li>避免摄像头抖动，保持画面稳定</li>
							<li>定期清洁摄像头镜头</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>


<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import request from '/@/utils/request';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
import type { UploadInstance, UploadProps } from 'element-plus';
import { SocketService } from '/@/utils/socket';
import { formatDate } from '/@/utils/formatTime';

const stores = useUserInfo();
const conf = ref('');
const kind = ref('');
const weight = ref('');
const { userInfos } = storeToRefs(stores);

const state = reactive({
	weight_items: [] as any,
	kind_items: [
		{
			value: 'corn',
			label: '玉米',
		},
		{
			value: 'rice',
			label: '水稻',
		},
		{
			value: 'strawberry',
			label: '草莓',
		},
		{
			value: 'tomato',
			label: '西红柿',
		},
	],
	data: {} as any,
	video_path: '',
	type_text: "正在保存",
	percentage: 50,
	isShow: false,
	cameraisShow: false,
	form: {
		username: '',
		weight: '',
		conf: null as any,
		kind: '',
		startTime: ''
	},
});

const socketService = new SocketService();

socketService.on('message', (data) => {
	console.log('Received message:', data);
	ElMessage.success(data);
});

const formatTooltip = (val: number) => {
	return val / 100
}

socketService.on('progress', (data) => {
	state.percentage = parseInt(data);
	if (parseInt(data) < 100) {
		state.isShow = true;
	} else {
		//两秒后隐藏进度条
		ElMessage.success("保存成功！");
		setTimeout(() => {
			state.isShow = false;
			state.percentage = 0;
		}, 2000);
	}
	console.log('Received message:', data);
});

const getData = () => {
	request.get('/api/flask/file_names').then((res) => {
		if (res.code == 0) {
			res.data = JSON.parse(res.data);
			state.weight_items = res.data.weight_items.filter(item => item.value.includes(kind.value));
		} else {
			ElMessage.error(res.msg);
		}
	});
};


const start = () => {
	state.form.weight = weight.value;
	state.form.kind = kind.value;
	state.form.conf = (parseFloat(conf.value)/100);
	state.form.username = userInfos.value.userName;
	state.form.startTime = formatDate(new Date(), 'YYYY-mm-dd HH:MM:SS');
	console.log(state.form);
	const queryParams = new URLSearchParams(state.form).toString();
	state.cameraisShow = true
	state.video_path = `http://127.0.0.1:5000/predictCamera?${queryParams}`;
};

const stop = () => {
	request.get('/flask/stopCamera').then((res) => {
		if (res.code == 0) {
			res.data = JSON.parse(res.data);
			console.log(res.data);
			state.weight_items = res.data.weight_items;
		} else {
			ElMessage.error(res.msg);
		}
	});
	state.cameraisShow = false
};

onMounted(() => {
	getData();
});
</script>

<style scoped lang="scss">
.page-header {
	text-align: center;
	margin-bottom: var(--spacing-xl);
}

.control-panel {
	margin-bottom: var(--spacing-xl);

	.control-header {
		display: flex;
		align-items: center;
		gap: var(--spacing-sm);
		margin-bottom: var(--spacing-lg);
		font-size: 1.125rem;
		font-weight: 600;
		color: var(--text-primary);

		i {
			color: var(--primary-color);
			font-size: 1.25rem;
		}
	}

	.control-content {
		.control-row {
			display: grid;
			grid-template-columns: 1fr 1fr 2fr;
			gap: var(--spacing-lg);
			align-items: end;
			margin-bottom: var(--spacing-lg);
		}

		.control-item {
			display: flex;
			flex-direction: column;
			gap: var(--spacing-sm);

			.control-label {
				font-size: 0.875rem;
				font-weight: 500;
				color: var(--text-secondary);
			}

			.control-select {
				width: 100%;
			}
		}

		.confidence-item {
			.confidence-slider {
				display: flex;
				align-items: center;
				gap: var(--spacing-md);

				.slider {
					flex: 1;
				}

				.confidence-value {
					min-width: 40px;
					text-align: center;
					font-weight: 600;
					color: var(--primary-color);
				}
			}
		}

		.action-row {
			display: flex;
			gap: var(--spacing-lg);
			justify-content: center;
			margin-bottom: var(--spacing-lg);

			.start-button, .stop-button {
				height: 48px;
				padding: 0 var(--spacing-xl);
				font-weight: 600;
			}

			.stop-button {
				background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
				border: none;
				color: white;

				&:hover:not(:disabled) {
					transform: translateY(-2px);
					box-shadow: var(--shadow-lg);
				}

				&:disabled {
					background: var(--bg-secondary);
					color: var(--text-muted);
				}
			}
		}

		.progress-section {
			.progress-info {
				display: flex;
				align-items: center;
				gap: var(--spacing-sm);
				margin-bottom: var(--spacing-md);
				font-weight: 500;
				color: var(--text-secondary);

				i {
					color: var(--primary-color);
					animation: spin 1s linear infinite;
				}
			}

			.progress-bar {
				:deep(.el-progress-bar__outer) {
					background: var(--bg-secondary);
				}

				:deep(.el-progress-bar__inner) {
					background: var(--primary-gradient);
				}
			}
		}
	}
}

.camera-display {
	margin-bottom: var(--spacing-xl);

	.camera-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: var(--spacing-lg);

		> div:first-child {
			display: flex;
			align-items: center;
			gap: var(--spacing-sm);
			font-size: 1.125rem;
			font-weight: 600;
			color: var(--text-primary);

			i {
				color: var(--primary-color);
				font-size: 1.25rem;
			}
		}

		.status-indicator {
			display: flex;
			align-items: center;
			gap: var(--spacing-sm);
			padding: var(--spacing-sm) var(--spacing-md);
			border-radius: var(--radius-md);
			background: var(--bg-secondary);

			&.active {
				background: rgba(16, 185, 129, 0.1);

				.status-dot {
					background: var(--secondary-color);
					animation: pulse 2s infinite;
				}

				.status-text {
					color: var(--secondary-color);
				}
			}

			.status-dot {
				width: 8px;
				height: 8px;
				border-radius: 50%;
				background: var(--text-muted);
			}

			.status-text {
				font-size: 0.875rem;
				font-weight: 500;
				color: var(--text-muted);
			}
		}
	}

	.camera-container {
		width: 100%;
		height: 500px;
		border-radius: var(--radius-lg);
		overflow: hidden;
		background: var(--bg-secondary);
		position: relative;

		.camera-view {
			width: 100%;
			height: 100%;
			position: relative;

			.camera-stream {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.camera-overlay {
				position: absolute;
				top: var(--spacing-md);
				left: var(--spacing-md);

				.recording-indicator {
					display: flex;
					align-items: center;
					gap: var(--spacing-sm);
					padding: var(--spacing-sm) var(--spacing-md);
					background: rgba(220, 38, 38, 0.9);
					color: white;
					border-radius: var(--radius-md);
					font-size: 0.875rem;
					font-weight: 500;

					i {
						animation: blink 1s infinite;
					}
				}
			}
		}

		.camera-placeholder {
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			text-align: center;

			> i {
				font-size: 4rem;
				color: var(--text-muted);
				margin-bottom: var(--spacing-lg);
			}

			h3 {
				font-size: 1.5rem;
				font-weight: 600;
				color: var(--text-primary);
				margin-bottom: var(--spacing-sm);
			}

			> p {
				color: var(--text-secondary);
				margin-bottom: var(--spacing-xl);
			}

			.placeholder-features {
				display: flex;
				gap: var(--spacing-md);

				.feature-tag {
					display: flex;
					align-items: center;
					gap: var(--spacing-xs);
					padding: var(--spacing-sm) var(--spacing-md);
					background: var(--bg-card);
					border: 1px solid var(--border-light);
					border-radius: var(--radius-md);
					font-size: 0.875rem;
					color: var(--text-secondary);

					i {
						color: var(--primary-color);
					}
				}
			}
		}
	}
}

.realtime-features {
	margin-bottom: var(--spacing-xl);

	.features-header {
		display: flex;
		align-items: center;
		gap: var(--spacing-sm);
		margin-bottom: var(--spacing-lg);
		font-size: 1.125rem;
		font-weight: 600;
		color: var(--text-primary);

		i {
			color: var(--secondary-color);
			font-size: 1.25rem;
		}
	}

	.features-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: var(--spacing-lg);

		.feature-item {
			text-align: center;
			padding: var(--spacing-lg);
			background: var(--bg-secondary);
			border-radius: var(--radius-md);
			transition: all var(--transition-normal);

			&:hover {
				transform: translateY(-4px);
				box-shadow: var(--shadow-md);
			}

			i {
				font-size: 2.5rem;
				color: var(--secondary-color);
				margin-bottom: var(--spacing-md);
			}

			h4 {
				font-size: 1rem;
				font-weight: 600;
				color: var(--text-primary);
				margin-bottom: var(--spacing-sm);
			}

			p {
				font-size: 0.875rem;
				color: var(--text-secondary);
				line-height: 1.5;
			}
		}
	}
}

.usage-tips {
	.tips-header {
		display: flex;
		align-items: center;
		gap: var(--spacing-sm);
		margin-bottom: var(--spacing-lg);
		font-size: 1.125rem;
		font-weight: 600;
		color: var(--text-primary);

		i {
			color: var(--secondary-color);
			font-size: 1.25rem;
		}
	}

	.tips-content {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: var(--spacing-xl);

		.tip-section {
			h4 {
				font-size: 1rem;
				font-weight: 600;
				color: var(--text-primary);
				margin-bottom: var(--spacing-md);
			}

			ul {
				list-style: none;
				padding: 0;
				margin: 0;

				li {
					display: flex;
					align-items: flex-start;
					gap: var(--spacing-sm);
					margin-bottom: var(--spacing-sm);
					font-size: 0.875rem;
					color: var(--text-secondary);
					line-height: 1.5;

					&::before {
						content: '•';
						color: var(--primary-color);
						font-weight: bold;
						margin-top: 2px;
					}
				}
			}
		}
	}
}

// 动画效果
@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

@keyframes blink {
	0%, 50% {
		opacity: 1;
	}
	51%, 100% {
		opacity: 0.3;
	}
}

// 响应式设计
@media (max-width: 1024px) {
	.control-content .control-row {
		grid-template-columns: 1fr 1fr;

		.confidence-item {
			grid-column: 1 / -1;
		}
	}

	.features-grid {
		grid-template-columns: repeat(2, 1fr);
	}

	.tips-content {
		grid-template-columns: 1fr;
	}

	.placeholder-features {
		flex-direction: column;
		align-items: center;
	}
}

@media (max-width: 768px) {
	.control-content .control-row {
		grid-template-columns: 1fr;
	}

	.action-row {
		flex-direction: column;
		align-items: center;
	}

	.features-grid {
		grid-template-columns: 1fr;
	}

	.camera-header {
		flex-direction: column;
		gap: var(--spacing-md);
		align-items: flex-start;
	}
}
</style>